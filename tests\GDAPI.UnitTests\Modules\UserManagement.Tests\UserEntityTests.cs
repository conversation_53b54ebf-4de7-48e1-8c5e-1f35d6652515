using Xunit;
using FluentAssertions;
using GDAPI.Modules.UserManagement.Domain.Entities;

namespace GDAPI.UnitTests.Modules.UserManagement.Tests;

public class UserEntityTests
{
    [Fact]
    public void User_DefaultConstructor_SetsDefaultValues()
    {
        // Act
        var user = new User();

        // Assert
        user.Id.Should().Be(0);
        user.Username.Should().BeNull();
        user.Email.Should().BeNull();
        user.FirstName.Should().BeNull();
        user.LastName.Should().BeNull();
        user.FullName.Should().BeNull();
        user.Role.Should().BeNull();
        user.Department.Should().BeNull();
        user.Status.Should().BeNull();
        user.PhoneNumber.Should().BeNull();
        user.JobTitle.Should().BeNull();
        user.ManagerId.Should().BeNull();
        user.HireDate.Should().BeNull();
        user.LastLoginDate.Should().BeNull();
        user.ProfilePictureUrl.Should().BeNull();
        user.Bio.Should().BeNull();
        user.Skills.Should().BeNull();
        user.Certifications.Should().BeNull();
        user.EmergencyContactName.Should().BeNull();
        user.EmergencyContactPhone.Should().BeNull();
        user.Address.Should().BeNull();
        user.City.Should().BeNull();
        user.State.Should().BeNull();
        user.Country.Should().BeNull();
        user.PostalCode.Should().BeNull();
        user.TimeZone.Should().BeNull();
        user.PreferredLanguage.Should().BeNull();
        user.NotificationPreferences.Should().BeNull();
        user.TwoFactorEnabled.Should().BeFalse();
        user.LastPasswordChangeDate.Should().BeNull();
        user.PasswordExpiryDate.Should().BeNull();
        user.AccountLockedUntil.Should().BeNull();
        user.FailedLoginAttempts.Should().Be(0);
        user.IsActive.Should().BeFalse();
        user.CreatedAt.Should().Be(default(DateTime));
        user.UpdatedAt.Should().BeNull();
        user.CreatedBy.Should().BeNull();
        user.UpdatedBy.Should().BeNull();
    }

    [Fact]
    public void User_SetProperties_SetsValuesCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var hireDate = now.AddYears(-2);
        var lastLogin = now.AddDays(-1);

        // Act
        var user = new User
        {
            Id = 1,
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            FullName = "John Doe",
            Role = "Administrator",
            Department = "Information Technology",
            Status = "Active",
            PhoneNumber = "******-123-4567",
            JobTitle = "Senior System Administrator",
            ManagerId = 2,
            HireDate = hireDate,
            LastLoginDate = lastLogin,
            ProfilePictureUrl = "https://example.com/profiles/john.doe.jpg",
            Bio = "Experienced system administrator with 10+ years in IT",
            Skills = "Windows Server, Linux, PowerShell, Python",
            Certifications = "MCSA, CompTIA Security+, CISSP",
            EmergencyContactName = "Jane Doe",
            EmergencyContactPhone = "******-987-6543",
            Address = "123 Main Street",
            City = "New York",
            State = "NY",
            Country = "United States",
            PostalCode = "10001",
            TimeZone = "America/New_York",
            PreferredLanguage = "en-US",
            NotificationPreferences = "email,sms",
            TwoFactorEnabled = true,
            LastPasswordChangeDate = now.AddMonths(-3),
            PasswordExpiryDate = now.AddMonths(3),
            AccountLockedUntil = null,
            FailedLoginAttempts = 0,
            IsActive = true,
            CreatedAt = now.AddYears(-2),
            UpdatedAt = now,
            CreatedBy = "system",
            UpdatedBy = "admin"
        };

        // Assert
        user.Id.Should().Be(1);
        user.Username.Should().Be("john.doe");
        user.Email.Should().Be("<EMAIL>");
        user.FirstName.Should().Be("John");
        user.LastName.Should().Be("Doe");
        user.FullName.Should().Be("John Doe");
        user.Role.Should().Be("Administrator");
        user.Department.Should().Be("Information Technology");
        user.Status.Should().Be("Active");
        user.PhoneNumber.Should().Be("******-123-4567");
        user.JobTitle.Should().Be("Senior System Administrator");
        user.ManagerId.Should().Be(2);
        user.HireDate.Should().Be(hireDate);
        user.LastLoginDate.Should().Be(lastLogin);
        user.ProfilePictureUrl.Should().Be("https://example.com/profiles/john.doe.jpg");
        user.Bio.Should().Be("Experienced system administrator with 10+ years in IT");
        user.Skills.Should().Be("Windows Server, Linux, PowerShell, Python");
        user.Certifications.Should().Be("MCSA, CompTIA Security+, CISSP");
        user.EmergencyContactName.Should().Be("Jane Doe");
        user.EmergencyContactPhone.Should().Be("******-987-6543");
        user.Address.Should().Be("123 Main Street");
        user.City.Should().Be("New York");
        user.State.Should().Be("NY");
        user.Country.Should().Be("United States");
        user.PostalCode.Should().Be("10001");
        user.TimeZone.Should().Be("America/New_York");
        user.PreferredLanguage.Should().Be("en-US");
        user.NotificationPreferences.Should().Be("email,sms");
        user.TwoFactorEnabled.Should().BeTrue();
        user.LastPasswordChangeDate.Should().Be(now.AddMonths(-3));
        user.PasswordExpiryDate.Should().Be(now.AddMonths(3));
        user.AccountLockedUntil.Should().BeNull();
        user.FailedLoginAttempts.Should().Be(0);
        user.IsActive.Should().BeTrue();
        user.CreatedAt.Should().Be(now.AddYears(-2));
        user.UpdatedAt.Should().Be(now);
        user.CreatedBy.Should().Be("system");
        user.UpdatedBy.Should().Be("admin");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void User_EmptyOrNullUsername_AllowsEmptyValues(string username)
    {
        // Act
        var user = new User
        {
            Username = username
        };

        // Assert
        user.Username.Should().Be(username);
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("")]
    [InlineData(null)]
    public void User_EmailFormats_AllowsVariousFormats(string email)
    {
        // Act
        var user = new User
        {
            Email = email
        };

        // Assert
        user.Email.Should().Be(email);
    }

    [Fact]
    public void User_FailedLoginAttempts_AllowsNegativeValues()
    {
        // Act
        var user = new User
        {
            FailedLoginAttempts = -1
        };

        // Assert
        user.FailedLoginAttempts.Should().Be(-1);
    }

    [Fact]
    public void User_AccountLockedUntil_AllowsPastAndFutureDates()
    {
        // Arrange
        var pastDate = DateTime.UtcNow.AddDays(-1);
        var futureDate = DateTime.UtcNow.AddDays(1);

        // Act
        var userWithPastLock = new User { AccountLockedUntil = pastDate };
        var userWithFutureLock = new User { AccountLockedUntil = futureDate };

        // Assert
        userWithPastLock.AccountLockedUntil.Should().Be(pastDate);
        userWithFutureLock.AccountLockedUntil.Should().Be(futureDate);
    }

    [Fact]
    public void User_TwoFactorEnabled_DefaultsToFalse()
    {
        // Act
        var user = new User();

        // Assert
        user.TwoFactorEnabled.Should().BeFalse();
    }

    [Fact]
    public void User_IsActive_DefaultsToFalse()
    {
        // Act
        var user = new User();

        // Assert
        user.IsActive.Should().BeFalse();
    }

    [Fact]
    public void User_LongTextFields_HandlesLongStrings()
    {
        // Arrange
        var longBio = new string('a', 2000);
        var longSkills = new string('b', 1000);
        var longCertifications = new string('c', 1000);

        // Act
        var user = new User
        {
            Bio = longBio,
            Skills = longSkills,
            Certifications = longCertifications
        };

        // Assert
        user.Bio.Should().Be(longBio);
        user.Skills.Should().Be(longSkills);
        user.Certifications.Should().Be(longCertifications);
    }

    [Theory]
    [InlineData("America/New_York")]
    [InlineData("Europe/London")]
    [InlineData("Asia/Tokyo")]
    [InlineData("UTC")]
    [InlineData("")]
    [InlineData(null)]
    public void User_TimeZone_AllowsVariousTimeZones(string timeZone)
    {
        // Act
        var user = new User
        {
            TimeZone = timeZone
        };

        // Assert
        user.TimeZone.Should().Be(timeZone);
    }

    [Theory]
    [InlineData("en-US")]
    [InlineData("es-ES")]
    [InlineData("fr-FR")]
    [InlineData("de-DE")]
    [InlineData("")]
    [InlineData(null)]
    public void User_PreferredLanguage_AllowsVariousLanguages(string language)
    {
        // Act
        var user = new User
        {
            PreferredLanguage = language
        };

        // Assert
        user.PreferredLanguage.Should().Be(language);
    }

    [Fact]
    public void User_NullManagerId_AllowsNullValue()
    {
        // Act
        var user = new User
        {
            ManagerId = null
        };

        // Assert
        user.ManagerId.Should().BeNull();
    }

    [Fact]
    public void User_ValidManagerId_SetsValue()
    {
        // Act
        var user = new User
        {
            ManagerId = 5
        };

        // Assert
        user.ManagerId.Should().Be(5);
    }
}
