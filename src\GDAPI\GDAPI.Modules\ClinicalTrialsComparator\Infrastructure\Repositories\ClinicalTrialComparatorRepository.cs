using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Interfaces;
using GDAPI.Modules.ClinicalTrialsComparator.Infrastructure.Data;
using Dapper;

namespace GDAPI.Modules.ClinicalTrialsComparator.Infrastructure.Repositories;

/// <summary>
/// Clinical Trial Comparator repository implementation using ADO.NET with Dapper for ClinicalTrialsComparator module
/// </summary>
public class ClinicalTrialComparatorRepository : IClinicalTrialComparatorRepository
{
    private readonly IClinicalTrialsComparatorDbConnectionFactory _connectionFactory;

    public ClinicalTrialComparatorRepository(IClinicalTrialsComparatorDbConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
    }

    public async Task<IEnumerable<ClinicalTrialComparator>> GetClinicalTrialComparatorAsync(
        string? moleculeType = null,
        string? mechanismOfAction = null,
        string? assetName = null,
        string? indication = null,
        string? phase = null,
        string? trialName = null,
        string? umbrellaEndPoints = null,
        string? endPoints = null,
        string? eTimePoint = null,
        string? dataHandling = null,
        string? eventType = null,
        string? sTimepoint = null,
        string dataType = "Efficacy",
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        var parameters = new DynamicParameters();
        parameters.Add("@MoleculeType", moleculeType ?? string.Empty);
        parameters.Add("@MechanismofAction", mechanismOfAction ?? string.Empty);
        parameters.Add("@AssetName", assetName ?? string.Empty);
        parameters.Add("@indication", indication ?? string.Empty);
        parameters.Add("@Phase", phase ?? string.Empty);
        parameters.Add("@Trialname", trialName ?? string.Empty);
        parameters.Add("@UmbrellaEndPoints", umbrellaEndPoints ?? string.Empty);
        parameters.Add("@EndPoints", endPoints ?? string.Empty);
        parameters.Add("@ETimePoint", eTimePoint ?? string.Empty);
        parameters.Add("@DataHandling", dataHandling ?? string.Empty);
        parameters.Add("@event_type", eventType ?? string.Empty);
        parameters.Add("@Stimepoint", sTimepoint ?? string.Empty);
        parameters.Add("@Datatype", dataType);

        // Execute the stored procedure
        var results = await connection.QueryAsync<ClinicalTrialComparatorResult>(
            "GetCITrialResultsComparator_Grid",
            parameters,
            commandType: System.Data.CommandType.StoredProcedure);

        // Map results to domain entities
        var items = results.Select(MapToDomainEntity);

        return items;
    }

    private static ClinicalTrialComparator MapToDomainEntity(ClinicalTrialComparatorResult result)
    {
        return new ClinicalTrialComparator
        {
            Id = result.Id ?? 0,
            Asset = result.Asset,
            TrialName = result.TrialName,
            TrialPhase = result.TrialPhase,
            Indication = result.Indication,
            SubGroup = result.SubGroup,
            PortionOfStudy = result.PortionOfStudy,
            Dose = result.Dose,
            DosingRegimen = result.DosingRegimen,
            Roa = result.Roa,
            EventType = result.EventType,
            ResultPublication = result.ResultPublication,
            Timepoint = result.Timepoint,
            TimepointWeek = result.TimepointWeek,
            DrugSafety = result.DrugSafety,
            ControlSafety = result.ControlSafety,
            DifferenceSafety = result.DifferenceSafety,
            NDrug = result.NDrug,
            NControl = result.NControl,
            ActiveComparator = result.ActiveComparator,
            DataForDiff = result.DataForDiff,
            DataType = result.DataType,
            DateOfEntry = result.DateOfEntry,
            Misc = result.Misc,
            DrugAcronym = result.DrugAcronym,
            DrugBrandName = result.DrugBrandName,
            MoleculeType = result.MoleculeType,
            MechanismOfAction = result.MechanismOfAction,
            ProductCompany = result.ProductCompany,
            NctCode = result.NctCode,
            PrimaryTreatmentSetting = result.PrimaryTreatmentSetting,
            Age = result.Age,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };
    }
}

/// <summary>
/// Result class for mapping stored procedure results
/// </summary>
public class ClinicalTrialComparatorResult
{
    public int? Id { get; set; }
    public string? Asset { get; set; }
    public string? TrialName { get; set; }
    public string? TrialPhase { get; set; }
    public string? Indication { get; set; }
    public string? SubGroup { get; set; }
    public string? PortionOfStudy { get; set; }
    public string? Dose { get; set; }
    public string? DosingRegimen { get; set; }
    public string? Roa { get; set; }
    public string? EventType { get; set; }
    public string? ResultPublication { get; set; }
    public string? Timepoint { get; set; }
    public int? TimepointWeek { get; set; }
    public decimal? DrugSafety { get; set; }
    public decimal? ControlSafety { get; set; }
    public decimal? DifferenceSafety { get; set; }
    public int? NDrug { get; set; }
    public int? NControl { get; set; }
    public string? ActiveComparator { get; set; }
    public string? DataForDiff { get; set; }
    public string? DataType { get; set; }
    public DateTime? DateOfEntry { get; set; }
    public string? Misc { get; set; }
    public string? DrugAcronym { get; set; }
    public string? DrugBrandName { get; set; }
    public string? MoleculeType { get; set; }
    public string? MechanismOfAction { get; set; }
    public string? ProductCompany { get; set; }
    public string? NctCode { get; set; }
    public string? PrimaryTreatmentSetting { get; set; }
    public string? Age { get; set; }
}
