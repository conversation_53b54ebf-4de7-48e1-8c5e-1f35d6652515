using Xunit;
using FluentAssertions;
using GDAPI.Modules.AuthoringTool.Domain.Entities;

namespace GDAPI.UnitTests.Modules.AuthoringTool.Tests;

public class AuthoringEntityTests
{
    [Fact]
    public void Authoring_DefaultConstructor_SetsDefaultValues()
    {
        // Act
        var authoring = new Authoring();

        // Assert
        authoring.Id.Should().Be(0);
        authoring.Title.Should().Be(string.Empty);
        authoring.Description.Should().BeNull();
        authoring.AuthoringType.Should().Be(string.Empty);
        authoring.Category.Should().BeNull();
        authoring.Tags.Should().BeNull();
        authoring.Author.Should().Be(string.Empty);
        authoring.CoAuthors.Should().BeNull();
        authoring.Status.Should().Be("Draft");
        authoring.Version.Should().BeNull();
        authoring.FilePath.Should().BeNull();
        authoring.FileName.Should().BeNull();
        authoring.FileSize.Should().BeNull();
        authoring.FileFormat.Should().BeNull();
        authoring.LastModified.Should().BeNull();
        authoring.PublishedDate.Should().BeNull();
        authoring.Department.Should().BeNull();
        authoring.Project.Should().BeNull();
        authoring.Priority.Should().BeNull();
        authoring.DueDate.Should().BeNull();
        authoring.ReviewComments.Should().BeNull();
        authoring.ReviewedBy.Should().BeNull();
        authoring.ReviewedDate.Should().BeNull();
        authoring.ApprovedBy.Should().BeNull();
        authoring.ApprovedDate.Should().BeNull();
        authoring.IsActive.Should().BeTrue();
        authoring.IsDeleted.Should().BeFalse();
        authoring.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Authoring_SetProperties_UpdatesCorrectly()
    {
        // Arrange
        var authoring = new Authoring();
        var now = DateTime.UtcNow;

        // Act
        authoring.Title = "Technical Documentation";
        authoring.Description = "Comprehensive technical documentation for the system";
        authoring.AuthoringType = "Document";
        authoring.Category = "Technical";
        authoring.Tags = "technical, documentation, system";
        authoring.Author = "John Doe";
        authoring.CoAuthors = "Jane Smith, Bob Johnson";
        authoring.Status = "InReview";
        authoring.Version = "2.1";
        authoring.FilePath = "/documents/tech-doc.pdf";
        authoring.FileName = "tech-doc.pdf";
        authoring.FileSize = 2048000;
        authoring.FileFormat = "PDF";
        authoring.LastModified = now.AddDays(-1);
        authoring.PublishedDate = now.AddDays(-30);
        authoring.Department = "Engineering";
        authoring.Project = "Project Alpha";
        authoring.Priority = "High";
        authoring.DueDate = now.AddDays(7);
        authoring.ReviewComments = "Needs minor revisions";
        authoring.ReviewedBy = "Alice Manager";
        authoring.ReviewedDate = now.AddDays(-2);
        authoring.ApprovedBy = "Charlie Director";
        authoring.ApprovedDate = now.AddDays(-1);

        // Assert
        authoring.Title.Should().Be("Technical Documentation");
        authoring.Description.Should().Be("Comprehensive technical documentation for the system");
        authoring.AuthoringType.Should().Be("Document");
        authoring.Category.Should().Be("Technical");
        authoring.Tags.Should().Be("technical, documentation, system");
        authoring.Author.Should().Be("John Doe");
        authoring.CoAuthors.Should().Be("Jane Smith, Bob Johnson");
        authoring.Status.Should().Be("InReview");
        authoring.Version.Should().Be("2.1");
        authoring.FilePath.Should().Be("/documents/tech-doc.pdf");
        authoring.FileName.Should().Be("tech-doc.pdf");
        authoring.FileSize.Should().Be(2048000);
        authoring.FileFormat.Should().Be("PDF");
        authoring.LastModified.Should().Be(now.AddDays(-1));
        authoring.PublishedDate.Should().Be(now.AddDays(-30));
        authoring.Department.Should().Be("Engineering");
        authoring.Project.Should().Be("Project Alpha");
        authoring.Priority.Should().Be("High");
        authoring.DueDate.Should().Be(now.AddDays(7));
        authoring.ReviewComments.Should().Be("Needs minor revisions");
        authoring.ReviewedBy.Should().Be("Alice Manager");
        authoring.ReviewedDate.Should().Be(now.AddDays(-2));
        authoring.ApprovedBy.Should().Be("Charlie Director");
        authoring.ApprovedDate.Should().Be(now.AddDays(-1));
    }

    [Theory]
    [InlineData("Draft", "📝 Draft")]
    [InlineData("InReview", "👀 In Review")]
    [InlineData("Approved", "✅ Approved")]
    [InlineData("Published", "🚀 Published")]
    [InlineData("Archived", "📦 Archived")]
    [InlineData("CustomStatus", "CustomStatus")]
    public void StatusDisplay_WithVariousStatuses_ReturnsCorrectDisplay(string status, string expectedDisplay)
    {
        // Arrange
        var authoring = new Authoring { Status = status };

        // Act
        var statusDisplay = authoring.StatusDisplay;

        // Assert
        statusDisplay.Should().Be(expectedDisplay);
    }

    [Fact]
    public void AuthorInfo_WithCoAuthors_ReturnsFormattedAuthorInfo()
    {
        // Arrange
        var authoring = new Authoring
        {
            Author = "John Doe",
            CoAuthors = "Jane Smith, Bob Johnson"
        };

        // Act
        var authorInfo = authoring.AuthorInfo;

        // Assert
        authorInfo.Should().Be("John Doe (with Jane Smith, Bob Johnson)");
    }

    [Fact]
    public void AuthorInfo_WithoutCoAuthors_ReturnsAuthorOnly()
    {
        // Arrange
        var authoring = new Authoring
        {
            Author = "John Doe",
            CoAuthors = null
        };

        // Act
        var authorInfo = authoring.AuthorInfo;

        // Assert
        authorInfo.Should().Be("John Doe");
    }

    [Fact]
    public void AuthorInfo_WithEmptyCoAuthors_ReturnsAuthorOnly()
    {
        // Arrange
        var authoring = new Authoring
        {
            Author = "John Doe",
            CoAuthors = ""
        };

        // Act
        var authorInfo = authoring.AuthorInfo;

        // Assert
        authorInfo.Should().Be("John Doe");
    }

    [Theory]
    [InlineData(512, "512 B")]
    [InlineData(1536, "1.5 KB")]
    [InlineData(1048576, "1.0 MB")]
    [InlineData(2097152, "2.0 MB")]
    [InlineData(1073741824, "1.0 GB")]
    [InlineData(2147483648, "2.0 GB")]
    public void FileSizeDisplay_WithVariousFileSizes_ReturnsFormattedSize(long fileSize, string expectedDisplay)
    {
        // Arrange
        var authoring = new Authoring { FileSize = fileSize };

        // Act
        var fileSizeDisplay = authoring.FileSizeDisplay;

        // Assert
        fileSizeDisplay.Should().Be(expectedDisplay);
    }

    [Fact]
    public void FileSizeDisplay_WithNullFileSize_ReturnsNA()
    {
        // Arrange
        var authoring = new Authoring { FileSize = null };

        // Act
        var fileSizeDisplay = authoring.FileSizeDisplay;

        // Assert
        fileSizeDisplay.Should().Be("N/A");
    }

    [Fact]
    public void Authoring_InheritsFromBaseEntity_HasBaseEntityProperties()
    {
        // Arrange
        var authoring = new Authoring();

        // Act & Assert
        authoring.Should().BeAssignableTo<BaseEntity>();
        authoring.Id.Should().Be(0);
        authoring.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        authoring.IsActive.Should().BeTrue();
        authoring.IsDeleted.Should().BeFalse();
    }

    [Fact]
    public void Authoring_SetBaseEntityProperties_UpdatesCorrectly()
    {
        // Arrange
        var authoring = new Authoring();
        var now = DateTime.UtcNow;
        var rowVersion = new byte[] { 1, 2, 3, 4 };

        // Act
        authoring.Id = 123;
        authoring.CreatedAt = now;
        authoring.UpdatedAt = now.AddMinutes(5);
        authoring.CreatedBy = "user1";
        authoring.UpdatedBy = "user2";
        authoring.IsActive = false;
        authoring.IsDeleted = true;
        authoring.RowVersion = rowVersion;

        // Assert
        authoring.Id.Should().Be(123);
        authoring.CreatedAt.Should().Be(now);
        authoring.UpdatedAt.Should().Be(now.AddMinutes(5));
        authoring.CreatedBy.Should().Be("user1");
        authoring.UpdatedBy.Should().Be("user2");
        authoring.IsActive.Should().BeFalse();
        authoring.IsDeleted.Should().BeTrue();
        authoring.RowVersion.Should().BeEquivalentTo(rowVersion);
    }

    [Theory]
    [InlineData("Technical Report", "Document", "John Doe")]
    [InlineData("User Manual", "Guide", "Jane Smith")]
    [InlineData("API Documentation", "Reference", "Bob Johnson")]
    public void Authoring_SetVariousValues_UpdatesCorrectly(string title, string authoringType, string author)
    {
        // Arrange
        var authoring = new Authoring();

        // Act
        authoring.Title = title;
        authoring.AuthoringType = authoringType;
        authoring.Author = author;

        // Assert
        authoring.Title.Should().Be(title);
        authoring.AuthoringType.Should().Be(authoringType);
        authoring.Author.Should().Be(author);
    }

    [Fact]
    public void Authoring_WithComplexScenario_HandlesAllProperties()
    {
        // Arrange & Act
        var authoring = new Authoring
        {
            Title = "Complex Document",
            Description = "A complex document with all properties set",
            AuthoringType = "Technical Document",
            Category = "Engineering",
            Tags = "complex, comprehensive, detailed",
            Author = "Lead Author",
            CoAuthors = "Co-Author 1, Co-Author 2",
            Status = "Published",
            Version = "3.2.1",
            FilePath = "/complex/path/document.pdf",
            FileName = "complex-document.pdf",
            FileSize = 5242880, // 5 MB
            FileFormat = "PDF",
            Department = "Research & Development",
            Project = "Complex Project",
            Priority = "Critical"
        };

        // Assert
        authoring.StatusDisplay.Should().Be("🚀 Published");
        authoring.AuthorInfo.Should().Be("Lead Author (with Co-Author 1, Co-Author 2)");
        authoring.FileSizeDisplay.Should().Be("5.0 MB");
        authoring.Title.Should().Be("Complex Document");
        authoring.AuthoringType.Should().Be("Technical Document");
        authoring.Priority.Should().Be("Critical");
    }
}
