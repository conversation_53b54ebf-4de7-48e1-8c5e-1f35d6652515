{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=GDSolutionDB_Test;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "GDUploadDatabaseConnection": "Server=(localdb)\\mssqllocaldb;Database=GDSolutionDB_Test;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "ApiKeySettings": {"HeaderName": "X-API-Key", "QueryParameterName": "apikey", "AllowQueryParameter": true, "DefaultRateLimit": 1000, "RateLimitWindowMinutes": 60, "TestApiKey": "test-api-key-for-integration-tests"}, "JwtSettings": {"SecretKey": "TestSecretKeyThatIsAtLeast32CharactersLongForTesting!", "Issuer": "GDSolution-Test", "Audience": "GDSolution-Test", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "EmailSettings": {"SmtpServer": "localhost", "SmtpPort": 25, "SenderEmail": "<EMAIL>", "SenderName": "GD Solution Test", "Username": "", "Password": "", "EnableSsl": false}, "ApplicationSettings": {"ApplicationName": "GD Solution Test", "ApplicationVersion": "1.0.0", "Environment": "Test", "EnableDetailedErrors": true, "MaxLoginAttempts": 5, "LockoutDurationInMinutes": 30}}