# ClinicalTrialsComparator Module

## 🎯 Overview

The **ClinicalTrialsComparator module** provides comprehensive clinical trial comparison data management capabilities. This module follows the same architectural pattern as other modules in the solution and integrates with the `GetCITrialResultsComparator_Grid` stored procedure.

## 📁 Module Structure

```
GDAPI.Modules.ClinicalTrialsComparator/
├── Domain/
│   ├── Entities/
│   │   └── ClinicalTrialComparator.cs      # Domain entity
│   └── Interfaces/
│       └── IClinicalTrialComparatorRepository.cs  # Repository interface
├── Application/
│   ├── DTOs/
│   │   └── ClinicalTrialComparatorDto.cs   # Data transfer objects
│   └── Services/
│       ├── IClinicalTrialComparatorService.cs     # Service interface
│       └── ClinicalTrialComparatorService.cs      # Service implementation
└── Infrastructure/
    ├── Data/
    │   ├── IDbConnectionFactory.cs         # Connection factory interface
    │   └── SqlServerConnectionFactory.cs  # SQL Server implementation
    └── Repositories/
        └── ClinicalTrialComparatorRepository.cs   # Repository implementation
```

## 🔧 Features

### ✅ Repository Layer (ADO.NET + Dapper)
- **IClinicalTrialComparatorRepository** interface with comprehensive methods
- **ClinicalTrialComparatorRepository** implementation with async operations
- **Stored Procedure Integration** using `GetCITrialResultsComparator_Grid`
- **Advanced Filtering** and search capabilities

### ✅ Service Layer
- **IClinicalTrialComparatorService** interface with business logic
- **ClinicalTrialComparatorService** implementation with error handling
- **DTO Mapping** between entities and API responses
- **Comprehensive Logging** for debugging and monitoring

### ✅ API Layer
- **ClinicalTrialsComparatorController** with RESTful endpoints
- **Swagger Documentation** for all endpoints
- **Proper Error Handling** with consistent responses
- **Input Validation** using data annotations

## 📊 Data Model

The module handles the following clinical trial comparator data fields:

- **Trial Information**: asset, trial_name, trial_phase, indication, sub_group
- **Study Details**: portion_of_study, dose, dosing_regimen, roa, timepoint, timepoint_week
- **Safety Data**: drug_safety, control_safety, difference_safety, n_drug, n_control
- **Publication Info**: result_publication, event_type, data_for_diff, data_type
- **Drug Information**: drug_acronym, drug_brand_name, molecule_type, mechanism_of_action
- **Company Data**: product_company, active_comparator
- **Trial Metadata**: nct_code, primary_treatment_setting, age, date_of_entry, misc

## 🔧 Configuration

### Dependency Injection (Program.cs)
```csharp
// ClinicalTrialsComparator Module Services
builder.Services.AddScoped<IClinicalTrialsComparatorDbConnectionFactory, ClinicalTrialsComparatorSqlServerConnectionFactory>();
builder.Services.AddScoped<IClinicalTrialComparatorRepository, ClinicalTrialComparatorRepository>();
builder.Services.AddScoped<IClinicalTrialComparatorService, ClinicalTrialComparatorService>();
```

### Database Connection
Uses the same `DefaultConnection` connection string as other modules.

## 📝 API Endpoints

### GET /api/clinicaltrials-comparator
Get paginated clinical trial comparator data with filtering support.

**Query Parameters:**
- `pageNumber` (int): Page number (default: 1)
- `pageSize` (int): Page size (default: 10, max: 100)
- `searchTerm` (string): Search term for filtering
- `assetFilter` (string): Filter by asset
- `trialPhaseFilter` (string): Filter by trial phase
- `indicationFilter` (string): Filter by indication
- `eventTypeFilter` (string): Filter by event type
- `productCompanyFilter` (string): Filter by product company

### GET /api/clinicaltrials-comparator/filters
Get available filter options for dropdown lists.

### GET /api/clinicaltrials-comparator/stats
Get clinical trial comparator statistics.

### GET /api/clinicaltrials-comparator/health
Health check endpoint for the module.

## 📝 Usage Examples

### Basic Clinical Trial Comparator Retrieval
```bash
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?pageSize=5"
```

### Search Clinical Trial Data
```bash
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?searchTerm=oncology&pageSize=10"
```

### Filter by Trial Phase
```bash
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?trialPhaseFilter=Phase%20III"
```

### Filter by Product Company
```bash
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?productCompanyFilter=Pfizer"
```

## 🏗️ Architecture Compliance

The ClinicalTrialsComparator module follows the **exact same pattern** as existing modules:

### ✅ Modular Independence
- Own database connection factory (`IClinicalTrialsComparatorDbConnectionFactory`)
- Own repository implementation (`ClinicalTrialComparatorRepository`)
- Own service layer (`ClinicalTrialComparatorService`)
- Own DTOs and domain models

### ✅ Clean Architecture
- **Domain**: Entities and interfaces
- **Application**: Services and DTOs
- **Infrastructure**: Data access and repositories
- **API**: Controllers in GDAPI.Web

### ✅ Dependency Injection
All services are properly registered in Program.cs with scoped lifetime.

## 🧪 Testing

### Quick Test Commands
```bash
# Health check
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator/health"

# Get all clinical trial comparator data
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator"

# Search clinical trial data
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?searchTerm=cancer"

# Filter by trial phase
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?trialPhaseFilter=Phase%20II"

# Filter by indication
curl -X GET "http://localhost:5199/api/clinicaltrials-comparator?indicationFilter=breast%20cancer"
```

## 📈 Next Steps

1. **Database Setup**: Ensure the `GetCITrialResultsComparator_Grid` stored procedure is available
2. **Testing**: Create unit and integration tests
3. **Documentation**: Add Swagger documentation examples
4. **Performance**: Optimize queries for large datasets
5. **Caching**: Implement caching for frequently accessed data
