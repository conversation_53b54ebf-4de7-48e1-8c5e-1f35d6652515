using System.ComponentModel.DataAnnotations;

namespace GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;

/// <summary>
/// Clinical Trial Comparator data transfer object for API responses
/// </summary>
public class ClinicalTrialComparatorDto
{
    public int Id { get; set; }
    public string? Asset { get; set; }
    public string? TrialName { get; set; }
    public string? TrialPhase { get; set; }
    public string? Indication { get; set; }
    public string? SubGroup { get; set; }
    public string? PortionOfStudy { get; set; }
    public string? Dose { get; set; }
    public string? DosingRegimen { get; set; }
    public string? Roa { get; set; }
    public string? EventType { get; set; }
    public string? ResultPublication { get; set; }
    public string? Timepoint { get; set; }
    public int? TimepointWeek { get; set; }
    public decimal? DrugSafety { get; set; }
    public decimal? ControlSafety { get; set; }
    public decimal? DifferenceSafety { get; set; }
    public int? NDrug { get; set; }
    public int? NControl { get; set; }
    public string? ActiveComparator { get; set; }
    public string? DataForDiff { get; set; }
    public string? DataType { get; set; }
    public DateTime? DateOfEntry { get; set; }
    public string? Misc { get; set; }
    public string? DrugAcronym { get; set; }
    public string? DrugBrandName { get; set; }
    public string? MoleculeType { get; set; }
    public string? MechanismOfAction { get; set; }
    public string? ProductCompany { get; set; }
    public string? NctCode { get; set; }
    public string? PrimaryTreatmentSetting { get; set; }
    public string? Age { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool IsActive { get; set; }

    // Computed properties
    public string TrialInfo { get; set; } = string.Empty;
    public string SafetyComparison { get; set; } = string.Empty;
    public string SampleSizeInfo { get; set; } = string.Empty;
    public string CompanyDrugInfo { get; set; } = string.Empty;
}

/// <summary>
/// Clinical Trial Comparator search request DTO matching stored procedure parameters
/// </summary>
public class ClinicalTrialComparatorSearchRequest
{
    /// <summary>
    /// Molecule type filter (corresponds to @MoleculeType parameter)
    /// </summary>
    public string? MoleculeType { get; set; } = string.Empty;

    /// <summary>
    /// Mechanism of action filter (corresponds to @MechanismofAction parameter)
    /// </summary>
    public string? MechanismOfAction { get; set; } = string.Empty;

    /// <summary>
    /// Asset name filter (corresponds to @AssetName parameter)
    /// </summary>
    public string? AssetName { get; set; } = string.Empty;

    /// <summary>
    /// Indication filter (corresponds to @indication parameter)
    /// </summary>
    public string? Indication { get; set; } = string.Empty;

    /// <summary>
    /// Phase filter (corresponds to @Phase parameter)
    /// </summary>
    public string? Phase { get; set; } = string.Empty;

    /// <summary>
    /// Trial name filter (corresponds to @Trialname parameter)
    /// </summary>
    public string? TrialName { get; set; } = string.Empty;

    /// <summary>
    /// Umbrella endpoints filter (corresponds to @UmbrellaEndPoints parameter)
    /// </summary>
    public string? UmbrellaEndPoints { get; set; } = string.Empty;

    /// <summary>
    /// Endpoints filter (corresponds to @EndPoints parameter)
    /// </summary>
    public string? EndPoints { get; set; } = string.Empty;

    /// <summary>
    /// E timepoint filter (corresponds to @ETimePoint parameter)
    /// </summary>
    public string? ETimePoint { get; set; } = string.Empty;

    /// <summary>
    /// Data handling filter (corresponds to @DataHandling parameter)
    /// </summary>
    public string? DataHandling { get; set; } = string.Empty;

    /// <summary>
    /// Event type filter (corresponds to @event_type parameter)
    /// </summary>
    public string? EventType { get; set; } = string.Empty;

    /// <summary>
    /// S timepoint filter (corresponds to @Stimepoint parameter)
    /// </summary>
    public string? STimepoint { get; set; } = string.Empty;

    /// <summary>
    /// Data type filter (corresponds to @Datatype parameter, default: 'Efficacy')
    /// </summary>
    public string DataType { get; set; } = "Efficacy";

    // Pagination properties (not part of stored procedure)
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Clinical Trial Comparator filters DTO for dropdown lists
/// </summary>
public class ClinicalTrialComparatorFiltersDto
{
    public IEnumerable<string> MoleculeTypes { get; set; } = new List<string>();
    public IEnumerable<string> MechanismOfActions { get; set; } = new List<string>();
    public IEnumerable<string> AssetNames { get; set; } = new List<string>();
    public IEnumerable<string> Indications { get; set; } = new List<string>();
    public IEnumerable<string> Phases { get; set; } = new List<string>();
    public IEnumerable<string> TrialNames { get; set; } = new List<string>();
    public IEnumerable<string> UmbrellaEndPoints { get; set; } = new List<string>();
    public IEnumerable<string> EndPoints { get; set; } = new List<string>();
    public IEnumerable<string> ETimePoints { get; set; } = new List<string>();
    public IEnumerable<string> DataHandlings { get; set; } = new List<string>();
    public IEnumerable<string> EventTypes { get; set; } = new List<string>();
    public IEnumerable<string> STimepoints { get; set; } = new List<string>();
    public IEnumerable<string> DataTypes { get; set; } = new List<string>();
}

/// <summary>
/// Clinical Trial Comparator statistics DTO
/// </summary>
public class ClinicalTrialComparatorStatsDto
{
    public int TotalTrials { get; set; }
    public int UniqueAssets { get; set; }
    public int UniqueIndications { get; set; }
    public int UniqueCompanies { get; set; }
    public int Phase1Trials { get; set; }
    public int Phase2Trials { get; set; }
    public int Phase3Trials { get; set; }
    public int Phase4Trials { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
