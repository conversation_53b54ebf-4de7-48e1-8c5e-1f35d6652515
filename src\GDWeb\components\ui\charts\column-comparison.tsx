
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';

interface ColumnComparisonChartProps {
  data: any[];
  title: string;
  config: {
    placeboCorrection: 'corrected' | 'side-by-side';
    showErrorBars: boolean;
    showPValues: boolean;
    sortOrder: 'drug-name' | 'endpoint';
  };
}

export const ColumnComparisonChart: React.FC<ColumnComparisonChartProps> = ({ data, title, config }) => {
  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center border-2 border-dashed border-blue-300">
        <div className="text-center">
          <BarChart3 className="w-16 h-16 text-blue-400 mx-auto mb-4" />
          <p className="text-blue-600 font-medium text-lg">Column Comparison Chart</p>
          <p className="text-sm text-blue-500 mt-2">Select filters to display comparison data</p>
          <p className="text-xs text-blue-400 mt-1">Requirements: Less than 10 comparators</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Column comparison of efficacy data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center border-2 border-dashed border-blue-300">
          <div className="text-center">
            <BarChart3 className="w-16 h-16 text-blue-400 mx-auto mb-4" />
            <p className="text-blue-600 font-medium text-lg">Column Comparison Chart</p>
            <p className="text-sm text-blue-500 mt-2">Charting Requirements: Less than 10 comparators</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
