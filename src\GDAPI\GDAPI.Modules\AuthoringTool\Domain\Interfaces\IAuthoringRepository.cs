using GDAPI.Modules.AuthoringTool.Domain.Entities;

namespace GDAPI.Modules.AuthoringTool.Domain.Interfaces;

/// <summary>
/// Authoring-specific repository interface for AuthoringTool module
/// </summary>
public interface IAuthoringRepository
{
    /// <summary>
    /// Get paginated authoring items using stored procedure with filtering support
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10)</param>
    /// <param name="searchTerm">Optional search term for authoring title or description</param>
    /// <param name="statusFilter">Optional status filter</param>
    /// <param name="categoryFilter">Optional category filter</param>
    /// <param name="authorFilter">Optional author filter</param>
    /// <param name="departmentFilter">Optional department filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of authoring items with total count</returns>
    Task<(IEnumerable<Authoring> Items, int TotalCount)> GetAuthoringSamplePagedAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? searchTerm = null,
        string? statusFilter = null,
        string? categoryFilter = null,
        string? authorFilter = null,
        string? departmentFilter = null,
        CancellationToken cancellationToken = default);

    #region : Get User Details 

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves user details.

    /// <summary>
    /// 
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    Task<List<UserDetails>> GetAllUsersAsync(string type = "All");

    /// <summary>
    /// Retrieves a collection of clinical comparators grouped by indications.
    /// Each group contains the indication name and a list of associated clinical comparator entries.
    /// </summary>
    /// <returns>
    /// An asynchronous task that returns an enumerable collection of <see cref="ClinicalComparatorsGroupByIndications"/>,
    /// where each item represents a group of clinical comparators for a specific indication.
    /// </returns>
    Task<IEnumerable<ClinicalComparatorsGroupByIndications>> GetClinicalComparatorsAsync();

    #endregion

}
