using Xunit;
using FluentAssertions;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;

namespace GDAPI.UnitTests.Modules.ClinicalTrialsComparator.Tests;

public class ClinicalTrialComparatorEntityTests
{
    [Fact]
    public void ClinicalTrialComparator_DefaultConstructor_SetsDefaultValues()
    {
        // Act
        var clinicalTrial = new ClinicalTrialComparator();

        // Assert
        clinicalTrial.Id.Should().Be(0);
        clinicalTrial.TrialId.Should().BeNull();
        clinicalTrial.Title.Should().BeNull();
        clinicalTrial.Description.Should().BeNull();
        clinicalTrial.Phase.Should().BeNull();
        clinicalTrial.Status.Should().BeNull();
        clinicalTrial.Sponsor.Should().BeNull();
        clinicalTrial.PrimaryEndpoint.Should().BeNull();
        clinicalTrial.SecondaryEndpoints.Should().BeNull();
        clinicalTrial.TargetEnrollment.Should().BeNull();
        clinicalTrial.CurrentEnrollment.Should().BeNull();
        clinicalTrial.StartDate.Should().BeNull();
        clinicalTrial.EstimatedCompletionDate.Should().BeNull();
        clinicalTrial.ActualCompletionDate.Should().BeNull();
        clinicalTrial.StudyType.Should().BeNull();
        clinicalTrial.InterventionType.Should().BeNull();
        clinicalTrial.PrimaryCondition.Should().BeNull();
        clinicalTrial.SecondaryConditions.Should().BeNull();
        clinicalTrial.InclusionCriteria.Should().BeNull();
        clinicalTrial.ExclusionCriteria.Should().BeNull();
        clinicalTrial.StudyDesign.Should().BeNull();
        clinicalTrial.PrimaryOutcomeMeasures.Should().BeNull();
        clinicalTrial.SecondaryOutcomeMeasures.Should().BeNull();
        clinicalTrial.StudyLocations.Should().BeNull();
        clinicalTrial.PrincipalInvestigator.Should().BeNull();
        clinicalTrial.ContactInformation.Should().BeNull();
        clinicalTrial.EthicsApprovalNumber.Should().BeNull();
        clinicalTrial.RegistryUrl.Should().BeNull();
        clinicalTrial.PublicationsUrl.Should().BeNull();
        clinicalTrial.DataSharingPlan.Should().BeNull();
        clinicalTrial.FundingSource.Should().BeNull();
        clinicalTrial.BudgetAmount.Should().BeNull();
        clinicalTrial.CurrencyCode.Should().BeNull();
        clinicalTrial.RegulatoryApprovals.Should().BeNull();
        clinicalTrial.AdverseEventsReported.Should().BeNull();
        clinicalTrial.DataMonitoringCommittee.Should().BeNull();
        clinicalTrial.InterimAnalysisPlanned.Should().BeNull();
        clinicalTrial.StatisticalAnalysisPlan.Should().BeNull();
        clinicalTrial.QualityAssurancePlan.Should().BeNull();
        clinicalTrial.RiskAssessment.Should().BeNull();
        clinicalTrial.IsActive.Should().BeFalse();
        clinicalTrial.CreatedAt.Should().Be(default(DateTime));
        clinicalTrial.UpdatedAt.Should().BeNull();
        clinicalTrial.CreatedBy.Should().BeNull();
        clinicalTrial.UpdatedBy.Should().BeNull();
    }

    [Fact]
    public void ClinicalTrialComparator_SetProperties_SetsValuesCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var startDate = now.AddMonths(-6);
        var completionDate = now.AddMonths(18);

        // Act
        var clinicalTrial = new ClinicalTrialComparator
        {
            Id = 1,
            TrialId = "NCT12345678",
            Title = "Cancer Treatment Study",
            Description = "A comprehensive phase III study for advanced cancer treatment",
            Phase = "Phase III",
            Status = "Active",
            Sponsor = "Pharma Corporation",
            PrimaryEndpoint = "Overall Survival",
            SecondaryEndpoints = "Progression-free survival, Quality of life",
            TargetEnrollment = 500,
            CurrentEnrollment = 250,
            StartDate = startDate,
            EstimatedCompletionDate = completionDate,
            StudyType = "Interventional",
            InterventionType = "Drug",
            PrimaryCondition = "Advanced Cancer",
            SecondaryConditions = "Metastatic disease",
            InclusionCriteria = "Age 18-75, confirmed diagnosis",
            ExclusionCriteria = "Pregnancy, severe comorbidities",
            StudyDesign = "Randomized, double-blind, placebo-controlled",
            PrimaryOutcomeMeasures = "Overall survival at 2 years",
            SecondaryOutcomeMeasures = "Progression-free survival, safety",
            StudyLocations = "Multiple international sites",
            PrincipalInvestigator = "Dr. John Smith",
            ContactInformation = "<EMAIL>",
            EthicsApprovalNumber = "IRB-2023-001",
            RegistryUrl = "https://clinicaltrials.gov/ct2/show/NCT12345678",
            PublicationsUrl = "https://pubmed.ncbi.nlm.nih.gov/",
            DataSharingPlan = "Data will be shared upon request",
            FundingSource = "Pharma Corporation",
            BudgetAmount = 5000000.00m,
            CurrencyCode = "USD",
            RegulatoryApprovals = "FDA, EMA approved",
            AdverseEventsReported = 15,
            DataMonitoringCommittee = "Independent DMC established",
            InterimAnalysisPlanned = true,
            StatisticalAnalysisPlan = "Intention-to-treat analysis",
            QualityAssurancePlan = "GCP compliant monitoring",
            RiskAssessment = "Low to moderate risk",
            IsActive = true,
            CreatedAt = now,
            UpdatedAt = now.AddDays(1),
            CreatedBy = "system",
            UpdatedBy = "admin"
        };

        // Assert
        clinicalTrial.Id.Should().Be(1);
        clinicalTrial.TrialId.Should().Be("NCT12345678");
        clinicalTrial.Title.Should().Be("Cancer Treatment Study");
        clinicalTrial.Description.Should().Be("A comprehensive phase III study for advanced cancer treatment");
        clinicalTrial.Phase.Should().Be("Phase III");
        clinicalTrial.Status.Should().Be("Active");
        clinicalTrial.Sponsor.Should().Be("Pharma Corporation");
        clinicalTrial.PrimaryEndpoint.Should().Be("Overall Survival");
        clinicalTrial.SecondaryEndpoints.Should().Be("Progression-free survival, Quality of life");
        clinicalTrial.TargetEnrollment.Should().Be(500);
        clinicalTrial.CurrentEnrollment.Should().Be(250);
        clinicalTrial.StartDate.Should().Be(startDate);
        clinicalTrial.EstimatedCompletionDate.Should().Be(completionDate);
        clinicalTrial.StudyType.Should().Be("Interventional");
        clinicalTrial.InterventionType.Should().Be("Drug");
        clinicalTrial.PrimaryCondition.Should().Be("Advanced Cancer");
        clinicalTrial.SecondaryConditions.Should().Be("Metastatic disease");
        clinicalTrial.InclusionCriteria.Should().Be("Age 18-75, confirmed diagnosis");
        clinicalTrial.ExclusionCriteria.Should().Be("Pregnancy, severe comorbidities");
        clinicalTrial.StudyDesign.Should().Be("Randomized, double-blind, placebo-controlled");
        clinicalTrial.PrimaryOutcomeMeasures.Should().Be("Overall survival at 2 years");
        clinicalTrial.SecondaryOutcomeMeasures.Should().Be("Progression-free survival, safety");
        clinicalTrial.StudyLocations.Should().Be("Multiple international sites");
        clinicalTrial.PrincipalInvestigator.Should().Be("Dr. John Smith");
        clinicalTrial.ContactInformation.Should().Be("<EMAIL>");
        clinicalTrial.EthicsApprovalNumber.Should().Be("IRB-2023-001");
        clinicalTrial.RegistryUrl.Should().Be("https://clinicaltrials.gov/ct2/show/NCT12345678");
        clinicalTrial.PublicationsUrl.Should().Be("https://pubmed.ncbi.nlm.nih.gov/");
        clinicalTrial.DataSharingPlan.Should().Be("Data will be shared upon request");
        clinicalTrial.FundingSource.Should().Be("Pharma Corporation");
        clinicalTrial.BudgetAmount.Should().Be(5000000.00m);
        clinicalTrial.CurrencyCode.Should().Be("USD");
        clinicalTrial.RegulatoryApprovals.Should().Be("FDA, EMA approved");
        clinicalTrial.AdverseEventsReported.Should().Be(15);
        clinicalTrial.DataMonitoringCommittee.Should().Be("Independent DMC established");
        clinicalTrial.InterimAnalysisPlanned.Should().BeTrue();
        clinicalTrial.StatisticalAnalysisPlan.Should().Be("Intention-to-treat analysis");
        clinicalTrial.QualityAssurancePlan.Should().Be("GCP compliant monitoring");
        clinicalTrial.RiskAssessment.Should().Be("Low to moderate risk");
        clinicalTrial.IsActive.Should().BeTrue();
        clinicalTrial.CreatedAt.Should().Be(now);
        clinicalTrial.UpdatedAt.Should().Be(now.AddDays(1));
        clinicalTrial.CreatedBy.Should().Be("system");
        clinicalTrial.UpdatedBy.Should().Be("admin");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void ClinicalTrialComparator_EmptyOrNullTrialId_AllowsEmptyValues(string trialId)
    {
        // Act
        var clinicalTrial = new ClinicalTrialComparator
        {
            TrialId = trialId
        };

        // Assert
        clinicalTrial.TrialId.Should().Be(trialId);
    }

    [Fact]
    public void ClinicalTrialComparator_NegativeEnrollmentNumbers_AllowsNegativeValues()
    {
        // Act
        var clinicalTrial = new ClinicalTrialComparator
        {
            TargetEnrollment = -100,
            CurrentEnrollment = -50
        };

        // Assert
        clinicalTrial.TargetEnrollment.Should().Be(-100);
        clinicalTrial.CurrentEnrollment.Should().Be(-50);
    }

    [Fact]
    public void ClinicalTrialComparator_FutureDates_AllowsFutureDates()
    {
        // Arrange
        var futureDate = DateTime.UtcNow.AddYears(5);

        // Act
        var clinicalTrial = new ClinicalTrialComparator
        {
            StartDate = futureDate,
            EstimatedCompletionDate = futureDate.AddMonths(12)
        };

        // Assert
        clinicalTrial.StartDate.Should().Be(futureDate);
        clinicalTrial.EstimatedCompletionDate.Should().Be(futureDate.AddMonths(12));
    }

    [Fact]
    public void ClinicalTrialComparator_LargeBudgetAmount_HandlesLargeDecimals()
    {
        // Act
        var clinicalTrial = new ClinicalTrialComparator
        {
            BudgetAmount = 999999999.99m
        };

        // Assert
        clinicalTrial.BudgetAmount.Should().Be(999999999.99m);
    }
}
