@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: Roboto, sans-serif;
  --font-mono: Fira Code, monospace;
  --font-serif: Playfair Display, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.5rem;
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.1870 0.0063 271.0667);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1870 0.0063 271.0667);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1870 0.0063 271.0667);
  --primary: oklch(0.2140 0.0746 268.2592);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9700 0.0029 264.5420);
  --secondary-foreground: oklch(0.1870 0.0063 271.0667);
  --muted: oklch(0.9700 0.0029 264.5420);
  --muted-foreground: oklch(0.6999 0.0029 286.3252);
  --accent: oklch(0.7701 0.1428 231.9429);
  --accent-foreground: oklch(0.2140 0.0746 268.2592);
  --destructive: oklch(0.5549 0.2154 27.2803);
  --border: oklch(0.9128 0 0);
  --input: oklch(0.9128 0 0);
  --ring: oklch(0.2140 0.0746 268.2592);
  --chart-1: oklch(0.2140 0.0746 268.2592);
  --chart-2: oklch(0.7701 0.1428 231.9429);
  --chart-3: oklch(0.5561 0.1445 246.4773);
  --chart-4: oklch(0.2907 0.1230 260.6446);
  --chart-5: oklch(0.9026 0.0638 222.9706);
  --sidebar: oklch(0.9700 0.0029 264.5420);
  --sidebar-foreground: oklch(0.1870 0.0063 271.0667);
  --sidebar-primary: oklch(0.2140 0.0746 268.2592);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.7701 0.1428 231.9429);
  --sidebar-accent-foreground: oklch(0.2140 0.0746 268.2592);
  --sidebar-border: oklch(0.9128 0 0);
  --sidebar-ring: oklch(0.2140 0.0746 268.2592);
  --destructive-foreground: oklch(1.0000 0 0);
  --font-sans: Roboto, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --shadow-color: hsl(255 86% 66%);
  --shadow-opacity: 0.2;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 2px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-sm: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow-md: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20);
  --shadow-lg: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20);
  --shadow-xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20);
  --shadow-2xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.50);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.1669 0.0498 265.2664);
  --foreground: oklch(1.0000 0 0);
  --card: oklch(0.2140 0.0746 268.2592);
  --card-foreground: oklch(1.0000 0 0);
  --popover: oklch(0.2140 0.0746 268.2592);
  --popover-foreground: oklch(1.0000 0 0);
  --primary: oklch(0.7701 0.1428 231.9429);
  --primary-foreground: oklch(0.2140 0.0746 268.2592);
  --secondary: oklch(0.1870 0.0063 271.0667);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.1870 0.0063 271.0667);
  --muted-foreground: oklch(0.6999 0.0029 286.3252);
  --accent: oklch(0.5561 0.1445 246.4773);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.5549 0.2154 27.2803);
  --border: oklch(0.3442 0.0075 264.4701);
  --input: oklch(0.3442 0.0075 264.4701);
  --ring: oklch(0.7701 0.1428 231.9429);
  --chart-1: oklch(0.7701 0.1428 231.9429);
  --chart-2: oklch(0.9026 0.0638 222.9706);
  --chart-3: oklch(0.5561 0.1445 246.4773);
  --chart-4: oklch(0.7468 0.1053 156.0919);
  --chart-5: oklch(0.8552 0.1341 89.5847);
  --sidebar: oklch(0.2140 0.0746 268.2592);
  --sidebar-foreground: oklch(1.0000 0 0);
  --sidebar-primary: oklch(0.7701 0.1428 231.9429);
  --sidebar-primary-foreground: oklch(0.2140 0.0746 268.2592);
  --sidebar-accent: oklch(0.5561 0.1445 246.4773);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3442 0.0075 264.4701);
  --sidebar-ring: oklch(0.7701 0.1428 231.9429);
  --destructive-foreground: oklch(1.0000 0 0);
  --radius: 0.5rem;
  --font-sans: Roboto, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --shadow-color: hsl(255 86% 66%);
  --shadow-opacity: 0.2;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 2px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-sm: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow-md: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20);
  --shadow-lg: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20);
  --shadow-xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20);
  --shadow-2xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.50);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}