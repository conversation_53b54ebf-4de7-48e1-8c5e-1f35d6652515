{"format": 1, "restore": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Web\\GDAPI.Web.csproj": {}}, "projects": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj", "projectName": "GDAPI.Modules.AuthoringTool", "projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\GDAPI.Modules.ClinicalTrialsComparator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\GDAPI.Modules.ClinicalTrialsComparator.csproj", "projectName": "GDAPI.Modules.ClinicalTrialsComparator", "projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\GDAPI.Modules.ClinicalTrialsComparator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj", "projectName": "GDAPI.Modules.UserManagement", "projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj", "projectName": "GDAPI.Shared", "projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Web\\GDAPI.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Web\\GDAPI.Web.csproj", "projectName": "GDAPI.Web", "projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Web\\GDAPI.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj"}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\GDAPI.Modules.ClinicalTrialsComparator.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\ClinicalTrialsComparator\\GDAPI.Modules.ClinicalTrialsComparator.csproj"}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj"}, "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"projectPath": "C:\\Avinash\\Avinash_CISolution\\CISolution\\src\\GDAPI\\GDAPI.Shared\\GDAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}