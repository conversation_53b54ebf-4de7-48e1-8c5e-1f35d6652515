using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Interfaces;
using GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Shared.Exceptions;
using Microsoft.Extensions.Logging;
using AutoMapper;

namespace GDAPI.Modules.ClinicalTrialsComparator.Application.Services;

/// <summary>
/// Clinical Trial Comparator service implementation for clinical trial comparison operations
/// </summary>
public class ClinicalTrialComparatorService : IClinicalTrialComparatorService
{
    private readonly IClinicalTrialComparatorRepository _repository;
    private readonly ILogger<ClinicalTrialComparatorService> _logger;
    private readonly IMapper _mapper;

    public ClinicalTrialComparatorService(
        IClinicalTrialComparatorRepository repository,
        ILogger<ClinicalTrialComparatorService> logger,
        IMapper mapper)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public async Task<ApiResponse<PagedResult<ClinicalTrialComparatorDto>>> GetClinicalTrialComparatorPagedAsync(
        ClinicalTrialComparatorSearchRequest request,
        CancellationToken cancellationToken = default)
    {
        // Validate request parameters
        if (request == null)
            throw new ValidationException("Request cannot be null").SetModule("ClinicalTrialsComparator");

        if (request.PageNumber < 1)
            throw new ValidationException("Page number must be greater than 0").SetModule("ClinicalTrialsComparator");

        if (request.PageSize < 1 || request.PageSize > 100)
            throw new ValidationException("Page size must be between 1 and 100").SetModule("ClinicalTrialsComparator");

        try
        {
            _logger.LogInformation("Getting clinical trial comparator data using stored procedure with filters: MoleculeType={MoleculeType}, AssetName={AssetName}, Indication={Indication}",
                request.MoleculeType, request.AssetName, request.Indication);

            var items = await _repository.GetClinicalTrialComparatorAsync(
                request.MoleculeType,
                request.MechanismOfAction,
                request.AssetName,
                request.Indication,
                request.Phase,
                request.TrialName,
                request.UmbrellaEndPoints,
                request.EndPoints,
                request.ETimePoint,
                request.DataHandling,
                request.EventType,
                request.STimepoint,
                request.DataType,
                cancellationToken);

            // Convert to list for pagination
            var itemsList = items.ToList();

            // Apply client-side pagination (since SP doesn't support pagination)
            var totalCount = itemsList.Count;
            var pagedItems = itemsList
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize);

            // Use AutoMapper to convert entities to DTOs
            var dtos = _mapper.Map<IEnumerable<ClinicalTrialComparatorDto>>(pagedItems);
            var pagedResult = new PagedResult<ClinicalTrialComparatorDto>(dtos, request.PageNumber, request.PageSize, totalCount);

            return ApiResponse<PagedResult<ClinicalTrialComparatorDto>>.SuccessResult(pagedResult, "Clinical trial comparator data retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving clinical trial comparator data");
            return ApiResponse<PagedResult<ClinicalTrialComparatorDto>>.ErrorResult("An error occurred while retrieving clinical trial comparator data");
        }
    }

    public async Task<ApiResponse<ClinicalTrialComparatorFiltersDto>> GetFiltersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting clinical trial comparator filter options");

            // For now, return empty filters - this would be implemented based on actual data needs
            var filters = new ClinicalTrialComparatorFiltersDto();

            return await Task.FromResult(ApiResponse<ClinicalTrialComparatorFiltersDto>.SuccessResult(filters, "Filter options retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving filter options");
            return ApiResponse<ClinicalTrialComparatorFiltersDto>.ErrorResult("An error occurred while retrieving filter options");
        }
    }

    public async Task<ApiResponse<ClinicalTrialComparatorStatsDto>> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting clinical trial comparator statistics");

            // For now, return empty stats - this would be implemented based on actual data needs
            var stats = new ClinicalTrialComparatorStatsDto();

            return await Task.FromResult(ApiResponse<ClinicalTrialComparatorStatsDto>.SuccessResult(stats, "Statistics retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving statistics");
            return ApiResponse<ClinicalTrialComparatorStatsDto>.ErrorResult("An error occurred while retrieving statistics");
        }
    }


}
