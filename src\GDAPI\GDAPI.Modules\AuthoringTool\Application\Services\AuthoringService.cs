using GDAPI.Modules.AuthoringTool.Domain.Entities;
using GDAPI.Modules.AuthoringTool.Domain.Interfaces;
using GDAPI.Modules.AuthoringTool.Application.DTOs;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Shared.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Data.SqlClient;

namespace GDAPI.Modules.AuthoringTool.Application.Services;

/// <summary>
/// Authoring service implementation
/// </summary>
public class AuthoringService : IAuthoringService
{
    private readonly IAuthoringRepository _authoringRepository;
    private readonly ILogger<AuthoringService> _logger;

    public AuthoringService(IAuthoringRepository authoringRepository, ILogger<AuthoringService> logger)
    {
        _authoringRepository = authoringRepository ?? throw new ArgumentNullException(nameof(authoringRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ApiResponse<PagedResult<AuthoringDto>>> GetAuthoringSamplePagedAsync(AuthoringSearchRequest request, CancellationToken cancellationToken = default)
    {
        // Validate request parameters
        if (request == null)
            throw new ValidationException("Request cannot be null").SetModule("AuthoringTool");

        if (request.PageNumber < 1)
            throw new ValidationException("Page number must be greater than 0").SetModule("AuthoringTool");

        if (request.PageSize < 1 || request.PageSize > 100)
            throw new ValidationException("Page size must be between 1 and 100").SetModule("AuthoringTool");

        try
        {
            _logger.LogInformation("Getting paginated authoring items using stored procedure. Page: {PageNumber}, Size: {PageSize}",
                request.PageNumber, request.PageSize);

            var (items, totalCount) = await _authoringRepository.GetAuthoringSamplePagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.CategoryFilter,
                request.AuthorFilter,
                request.DepartmentFilter,
                cancellationToken);

            var dtos = items.Select(MapToDto);
            var pagedResult = new PagedResult<AuthoringDto>(dtos, request.PageNumber, request.PageSize, totalCount);

            return ApiResponse<PagedResult<AuthoringDto>>.SuccessResult(pagedResult, "Authoring items retrieved successfully");
        }
        catch (SqlException sqlEx)
        {
            _logger.LogError(sqlEx, "Database error occurred while getting paginated authoring items");
            throw new DatabaseException("Failed to retrieve authoring items from database", sqlEx)
                .SetModule("AuthoringTool")
                .AddContext("PageNumber", request.PageNumber)
                .AddContext("PageSize", request.PageSize)
                .AddContext("SearchTerm", request.SearchTerm ?? "null");
        }
        catch (TimeoutException timeoutEx)
        {
            _logger.LogError(timeoutEx, "Timeout occurred while getting paginated authoring items");
            throw new DatabaseTimeoutException("Database operation timed out while retrieving authoring items", timeoutEx)
                .SetModule("AuthoringTool");
        }
        catch (Exception ex) when (!(ex is BaseException))
        {
            _logger.LogError(ex, "Unexpected error occurred while getting paginated authoring items");
            throw new BusinessException("An unexpected error occurred while retrieving authoring items", ex)
                .SetModule("AuthoringTool");
        }
    }

    private static AuthoringDto MapToDto(Authoring authoring)
    {
        return new AuthoringDto
        {
            Id = authoring.Id,
            Title = authoring.Title,
            Description = authoring.Description,
            AuthoringType = authoring.AuthoringType,
            Category = authoring.Category,
            Tags = authoring.Tags,
            Author = authoring.Author,
            CoAuthors = authoring.CoAuthors,
            Status = authoring.Status,
            Version = authoring.Version,
            FilePath = authoring.FilePath,
            FileName = authoring.FileName,
            FileSize = authoring.FileSize,
            FileFormat = authoring.FileFormat,
            LastModified = authoring.LastModified,
            PublishedDate = authoring.PublishedDate,
            Department = authoring.Department,
            Project = authoring.Project,
            Priority = authoring.Priority,
            DueDate = authoring.DueDate,
            ReviewComments = authoring.ReviewComments,
            ReviewedBy = authoring.ReviewedBy,
            ReviewedDate = authoring.ReviewedDate,
            ApprovedBy = authoring.ApprovedBy,
            ApprovedDate = authoring.ApprovedDate,
            IsActive = authoring.IsActive,
            CreatedAt = authoring.CreatedAt,
            UpdatedAt = authoring.UpdatedAt,
            StatusDisplay = authoring.StatusDisplay,
            AuthorInfo = authoring.AuthorInfo,
            FileSizeDisplay = authoring.FileSizeDisplay,
            IsOverdue = authoring.IsOverdue,
            PriorityDisplay = authoring.PriorityDisplay
        };
    }

    #region : Get User Details 

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves user details.

    ///<summary>
    /// Gets all users with optional type filter.
    /// </summary>
    /// <param name="type">User type filter (default: "All")</param>
    /// <returns>API response containing the list of users</returns>
    public async Task<ApiResponse<List<UserDetailsDto>>> GetAllUsersAsync(string type = "All")
    {
        try
        {
            var users = await _authoringRepository.GetAllUsersAsync(type);

            // Map UserDetails to UserDetailsDto
            var userDtos = users.Select(u => new UserDetailsDto
            {
                ShortCode = u.ShortCode,
                DisplayName = u.DisplayName,
                EmailAddress = u.EmailAddress,
                UserRole = u.UserRole,
                JobTitle = u.JobTitle,
                LastLogin = u.LastLogin,
                Status = u.Status
            }).ToList();

            return ApiResponse<List<UserDetailsDto>>.SuccessResult(userDtos, "Users retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving users");
            return ApiResponse<List<UserDetailsDto>>.ErrorResult("An error occurred while retrieving users");
        }
    }

    #endregion

    #region : Clinical Comparators

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves Clinical Comparators details.


    /// <summary>
    /// Retrieves a collection of clinical comparators grouped by indications.
    /// </summary>
    /// <returns>
    /// An asynchronous task that returns an enumerable collection of ClinicalComparatorsGroupByIndicationsDto,
    /// where each item represents a group of clinical comparators for a specific indication.
    /// </returns>
    public async Task<ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>> GetClinicalComparatorsAsync()
    {
        try
        {
            var groups = await _authoringRepository.GetClinicalComparatorsAsync();

            // Map ClinicalComparatorsGroupByIndications to ClinicalComparatorsGroupByIndicationsDto
            var groupDtos = groups.Select(g => new ClinicalComparatorsGroupByIndicationsDto
            {
                IndicationName = g.IndicationName,
                Entries = g.Entries.Select(e => new ClinicalComparatorsEntryDto
                {
                    Id = e.Id,
                    VersionName = e.VersionName,
                    Status = e.Status,
                    AccessType = e.AccessType,
                    IsAccessTypeFirewalled = e.IsAccessTypeFirewalled,
                    OwnerName = e.OwnerName,
                    ClientName = e.ClientName
                }).ToList()
            });

            return ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>.SuccessResult(groupDtos, "Clinical comparators retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving clinical comparators");
            return ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>.ErrorResult("An error occurred while retrieving clinical comparators");
        }
    }

    #endregion

}
