@model CIDashboard.Models.UserListViewModel
@{
    ViewData["Title"] = "Users Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Users Management</h3>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New User
                    </a>
                </div>
                
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="searchTerm">Search Users</label>
                                    <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                           value="@Model.SearchTerm" placeholder="Search by name or email...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="pageSize">Page Size</label>
                                    <select class="form-control" id="pageSize" name="pageSize">
                                        <option value="10" selected="@(Model.PageSize == 10)">10</option>
                                        <option value="25" selected="@(Model.PageSize == 25)">25</option>
                                        <option value="50" selected="@(Model.PageSize == 50)">50</option>
                                        <option value="100" selected="@(Model.PageSize == 100)">100</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-secondary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Users Table -->
                    @if (Model.Users.Items.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Roles</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users.Items)
                                    {
                                        <tr>
                                            <td>@user.Id</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(user.ProfileImageUrl))
                                                    {
                                                        <img src="@user.ProfileImageUrl" alt="@user.FullName" 
                                                             class="rounded-circle me-2" width="32" height="32">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 32px; height: 32px; color: white; font-size: 12px;">
                                                            @user.FirstName.Substring(0, 1)@user.LastName.Substring(0, 1)
                                                        </div>
                                                    }
                                                    <div>
                                                        <strong>@user.FullName</strong>
                                                        @if (user.LastLoginAt.HasValue)
                                                        {
                                                            <br><small class="text-muted">Last login: @user.LastLoginAt.Value.ToString("MMM dd, yyyy")</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @user.Email
                                                @if (user.EmailConfirmed)
                                                {
                                                    <i class="fas fa-check-circle text-success ms-1" title="Email Confirmed"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Email Not Confirmed"></i>
                                                }
                                            </td>
                                            <td>
                                                @user.PhoneNumber
                                                @if (user.PhoneConfirmed)
                                                {
                                                    <i class="fas fa-check-circle text-success ms-1" title="Phone Confirmed"></i>
                                                }
                                            </td>
                                            <td>
                                                @foreach (var role in user.Roles)
                                                {
                                                    <span class="badge bg-info me-1">@role</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive</span>
                                                }
                                                @if (user.TwoFactorEnabled)
                                                {
                                                    <span class="badge bg-primary ms-1">2FA</span>
                                                }
                                            </td>
                                            <td>@user.CreatedAt.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = user.Id })" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = user.Id })" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            title="Delete" onclick="confirmDelete(@user.Id, '@user.FullName')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.Users.TotalPages > 1)
                        {
                            <nav aria-label="Users pagination">
                                <ul class="pagination justify-content-center">
                                    @if (Model.Users.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { 
                                                pageNumber = Model.Users.PreviousPageNumber, 
                                                pageSize = Model.PageSize, 
                                                searchTerm = Model.SearchTerm 
                                            })">Previous</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.Users.PageNumber - 2); i <= Math.Min(Model.Users.TotalPages, Model.Users.PageNumber + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.Users.PageNumber ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Index", new { 
                                                pageNumber = i, 
                                                pageSize = Model.PageSize, 
                                                searchTerm = Model.SearchTerm 
                                            })">@i</a>
                                        </li>
                                    }

                                    @if (Model.Users.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { 
                                                pageNumber = Model.Users.NextPageNumber, 
                                                pageSize = Model.PageSize, 
                                                searchTerm = Model.SearchTerm 
                                            })">Next</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }

                        <!-- Results Info -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <small class="text-muted">
                                    Showing @((Model.Users.PageNumber - 1) * Model.Users.PageSize + 1) to 
                                    @Math.Min(Model.Users.PageNumber * Model.Users.PageSize, Model.Users.TotalCount) 
                                    of @Model.Users.TotalCount users
                                </small>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No users found</h5>
                            <p class="text-muted">@(string.IsNullOrEmpty(Model.SearchTerm) ? "There are no users in the system yet." : "No users match your search criteria.")</p>
                            @if (string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <a href="@Url.Action("Create")" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add First User
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(userId, userName) {
            document.getElementById('deleteUserName').textContent = userName;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + userId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
