using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text.Json;
using GDAPI.Shared.Authentication.Middleware;
using GDAPI.Shared.Authentication.Models;
using GDAPI.Shared.Authentication.Services;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.UnitTests.Shared.Tests.Authentication;

public class ApiKeyAuthenticationMiddlewareTests
{
    private readonly Mock<ILogger<ApiKeyAuthenticationMiddleware>> _loggerMock;
    private readonly Mock<IApiKeyService> _apiKeyServiceMock;
    private readonly Mock<IOptions<ApiKeySettings>> _settingsMock;
    private readonly ApiKeySettings _settings;
    private readonly RequestDelegate _nextMock;
    private readonly ApiKeyAuthenticationMiddleware _middleware;

    public ApiKeyAuthenticationMiddlewareTests()
    {
        _loggerMock = new Mock<ILogger<ApiKeyAuthenticationMiddleware>>();
        _apiKeyServiceMock = new Mock<IApiKeyService>();
        _settingsMock = new Mock<IOptions<ApiKeySettings>>();
        
        _settings = new ApiKeySettings
        {
            HeaderName = "X-API-Key",
            QueryParameterName = "apikey",
            AllowQueryParameter = true,
            DefaultRateLimit = 1000,
            RateLimitWindowMinutes = 60
        };
        
        _settingsMock.Setup(x => x.Value).Returns(_settings);
        
        _nextMock = (HttpContext context) => Task.CompletedTask;
        
        _middleware = new ApiKeyAuthenticationMiddleware(
            _nextMock, 
            _loggerMock.Object, 
            _settingsMock.Object, 
            _apiKeyServiceMock.Object);
    }

    [Theory]
    [InlineData("/health")]
    [InlineData("/swagger")]
    [InlineData("/swagger/index.html")]
    [InlineData("/swagger/v1/swagger.json")]
    [InlineData("/api/auth/generate-key")]
    public async Task InvokeAsync_PublicEndpoint_SkipsAuthentication(string endpoint)
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Request.Path = endpoint;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.OK);
        _apiKeyServiceMock.Verify(x => x.ValidateApiKeyAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task InvokeAsync_OptionsRequest_SkipsAuthentication()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Request.Method = "OPTIONS";
        context.Request.Path = "/api/users";

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.OK);
        _apiKeyServiceMock.Verify(x => x.ValidateApiKeyAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task InvokeAsync_NoApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        context.Request.Path = "/api/users";

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.Unauthorized);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("API key is required");
    }

    [Fact]
    public async Task InvokeAsync_ValidApiKeyInHeader_CallsNextMiddleware()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Request.Path = "/api/users";
        context.Request.Headers["X-API-Key"] = "valid-api-key";

        var validationResult = new ApiKeyValidationResult
        {
            IsValid = true,
            ApiKeyInfo = new ApiKeyInfo
            {
                Id = "key-id",
                Name = "Test Key",
                ClientName = "Test Client"
            },
            RemainingRequests = 999
        };

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("valid-api-key", It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.OK);
        context.Items["ApiKeyInfo"].Should().Be(validationResult.ApiKeyInfo);
        context.Items["RemainingRequests"].Should().Be(validationResult.RemainingRequests);
        
        _apiKeyServiceMock.Verify(x => x.ValidateApiKeyAsync("valid-api-key", It.IsAny<string>()), Times.Once);
        _apiKeyServiceMock.Verify(x => x.UpdateLastUsedAsync("valid-api-key"), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_ValidApiKeyInQueryParameter_CallsNextMiddleware()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Request.Path = "/api/users";
        context.Request.QueryString = new QueryString("?apikey=valid-api-key");

        var validationResult = new ApiKeyValidationResult
        {
            IsValid = true,
            ApiKeyInfo = new ApiKeyInfo
            {
                Id = "key-id",
                Name = "Test Key",
                ClientName = "Test Client"
            },
            RemainingRequests = 999
        };

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("valid-api-key", It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.OK);
        context.Items["ApiKeyInfo"].Should().Be(validationResult.ApiKeyInfo);
        
        _apiKeyServiceMock.Verify(x => x.ValidateApiKeyAsync("valid-api-key", It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_InvalidApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        context.Request.Path = "/api/users";
        context.Request.Headers["X-API-Key"] = "invalid-api-key";

        var validationResult = new ApiKeyValidationResult
        {
            IsValid = false,
            ErrorMessage = "Invalid API key"
        };

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("invalid-api-key", It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.Unauthorized);
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Invalid API key");
    }

    [Fact]
    public async Task InvokeAsync_RateLimitExceeded_ReturnsTooManyRequests()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        context.Request.Path = "/api/users";
        context.Request.Headers["X-API-Key"] = "rate-limited-key";

        var validationResult = new ApiKeyValidationResult
        {
            IsValid = false,
            RateLimitExceeded = true,
            ErrorMessage = "Rate limit exceeded",
            RemainingRequests = 0,
            ResetTime = DateTime.UtcNow.AddMinutes(30)
        };

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("rate-limited-key", It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.TooManyRequests);
        context.Response.Headers.Should().ContainKey("Retry-After");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Rate limit exceeded");
    }

    [Fact]
    public async Task InvokeAsync_ExceptionInValidation_ReturnsInternalServerError()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        context.Request.Path = "/api/users";
        context.Request.Headers["X-API-Key"] = "test-key";

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("test-key", It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.InternalServerError);
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Authentication error occurred");
    }

    [Fact]
    public async Task InvokeAsync_ValidRequest_AddsRateLimitHeaders()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Request.Path = "/api/users";
        context.Request.Headers["X-API-Key"] = "valid-api-key";

        var validationResult = new ApiKeyValidationResult
        {
            IsValid = true,
            ApiKeyInfo = new ApiKeyInfo
            {
                Id = "key-id",
                Name = "Test Key",
                ClientName = "Test Client",
                RateLimit = 1000
            },
            RemainingRequests = 999,
            ResetTime = DateTime.UtcNow.AddHours(1)
        };

        _apiKeyServiceMock
            .Setup(x => x.ValidateApiKeyAsync("valid-api-key", It.IsAny<string>()))
            .ReturnsAsync(validationResult);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.Headers.Should().ContainKey("X-RateLimit-Limit");
        context.Response.Headers.Should().ContainKey("X-RateLimit-Remaining");
        context.Response.Headers.Should().ContainKey("X-RateLimit-Reset");
        
        context.Response.Headers["X-RateLimit-Limit"].ToString().Should().Be("1000");
        context.Response.Headers["X-RateLimit-Remaining"].ToString().Should().Be("999");
    }
}
