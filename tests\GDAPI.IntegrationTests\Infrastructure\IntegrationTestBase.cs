using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.IntegrationTests.Infrastructure;

/// <summary>
/// Base class for integration tests providing common setup and utilities
/// </summary>
public abstract class IntegrationTestBase : IClassFixture<WebApplicationFactory<Program>>
{
    protected readonly WebApplicationFactory<Program> Factory;
    protected readonly HttpClient Client;
    protected readonly JsonSerializerOptions JsonOptions;

    protected IntegrationTestBase(WebApplicationFactory<Program> factory)
    {
        Factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Test");
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.Test.json", optional: false, reloadOnChange: true);
            });
            builder.ConfigureServices(services =>
            {
                // Override services for testing if needed
                services.AddLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Warning);
                });
            });
        });

        Client = Factory.CreateClient();
        
        // Add test API key for authentication
        Client.DefaultRequestHeaders.Add("X-API-Key", "test-api-key-for-integration-tests");

        JsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    /// <summary>
    /// Helper method to make GET requests and deserialize responses
    /// </summary>
    protected async Task<T?> GetAsync<T>(string requestUri)
    {
        var response = await Client.GetAsync(requestUri);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to make POST requests with JSON payload
    /// </summary>
    protected async Task<TResponse?> PostAsync<TRequest, TResponse>(string requestUri, TRequest request)
    {
        var response = await Client.PostAsJsonAsync(requestUri, request, JsonOptions);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<TResponse>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to make PUT requests with JSON payload
    /// </summary>
    protected async Task<TResponse?> PutAsync<TRequest, TResponse>(string requestUri, TRequest request)
    {
        var response = await Client.PutAsJsonAsync(requestUri, request, JsonOptions);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<TResponse>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to make DELETE requests
    /// </summary>
    protected async Task<TResponse?> DeleteAsync<TResponse>(string requestUri)
    {
        var response = await Client.DeleteAsync(requestUri);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<TResponse>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to get API response with error handling
    /// </summary>
    protected async Task<ApiResponse<T>?> GetApiResponseAsync<T>(string requestUri)
    {
        var response = await Client.GetAsync(requestUri);
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<ApiResponse<T>>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to post and get API response with error handling
    /// </summary>
    protected async Task<ApiResponse<TResponse>?> PostApiResponseAsync<TRequest, TResponse>(string requestUri, TRequest request)
    {
        var response = await Client.PostAsJsonAsync(requestUri, request, JsonOptions);
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<ApiResponse<TResponse>>(content, JsonOptions);
    }

    /// <summary>
    /// Helper method to create a test HTTP client without authentication
    /// </summary>
    protected HttpClient CreateUnauthenticatedClient()
    {
        return Factory.CreateClient();
    }

    /// <summary>
    /// Helper method to create a test HTTP client with custom API key
    /// </summary>
    protected HttpClient CreateClientWithApiKey(string apiKey)
    {
        var client = Factory.CreateClient();
        client.DefaultRequestHeaders.Add("X-API-Key", apiKey);
        return client;
    }

    /// <summary>
    /// Helper method to wait for a condition to be met (useful for async operations)
    /// </summary>
    protected async Task WaitForConditionAsync(Func<Task<bool>> condition, TimeSpan timeout, TimeSpan? interval = null)
    {
        interval ??= TimeSpan.FromMilliseconds(100);
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime)
        {
            if (await condition())
                return;

            await Task.Delay(interval.Value);
        }

        throw new TimeoutException($"Condition was not met within {timeout}");
    }

    /// <summary>
    /// Helper method to generate test data
    /// </summary>
    protected static string GenerateRandomString(int length = 10)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Helper method to generate test email
    /// </summary>
    protected static string GenerateTestEmail()
    {
        return $"test-{GenerateRandomString(8)}@example.com";
    }

    /// <summary>
    /// Helper method to generate test phone number
    /// </summary>
    protected static string GenerateTestPhoneNumber()
    {
        var random = new Random();
        return $"{random.Next(100, 999)}-{random.Next(100, 999)}-{random.Next(1000, 9999)}";
    }
}
