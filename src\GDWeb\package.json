{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3011 --hostname 0.0.0.0", "build": "next build", "start": "next start --port 3011 --hostname 0.0.0.0", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^15.2.3", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-resizable-panels": "^3.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/node": "22.16.5", "@types/react": "19.1.8", "@types/react-dom": "^19.0.4", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "tw-animate-css": "^1.3.4", "typescript": "5.8.3"}}