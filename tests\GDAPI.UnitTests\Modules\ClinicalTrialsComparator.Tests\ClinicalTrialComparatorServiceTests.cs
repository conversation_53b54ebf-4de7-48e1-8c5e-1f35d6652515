using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using AutoMapper;
using GDAPI.Modules.ClinicalTrialsComparator.Application.Services;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Interfaces;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;
using GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.UnitTests.Modules.ClinicalTrialsComparator.Tests;

public class ClinicalTrialComparatorServiceTests
{
    private readonly Mock<IClinicalTrialComparatorRepository> _repositoryMock;
    private readonly Mock<ILogger<ClinicalTrialComparatorService>> _loggerMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly ClinicalTrialComparatorService _service;

    public ClinicalTrialComparatorServiceTests()
    {
        _repositoryMock = new Mock<IClinicalTrialComparatorRepository>();
        _loggerMock = new Mock<ILogger<ClinicalTrialComparatorService>>();
        _mapperMock = new Mock<IMapper>();
        _service = new ClinicalTrialComparatorService(_repositoryMock.Object, _loggerMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task GetClinicalTrialsPagedAsync_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new ClinicalTrialComparatorSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "Cancer",
            StatusFilter = "Active",
            PhaseFilter = "Phase III",
            SponsorFilter = "Pharma Corp"
        };

        var clinicalTrials = new List<ClinicalTrialComparator>
        {
            new ClinicalTrialComparator
            {
                Id = 1,
                TrialId = "NCT12345678",
                Title = "Cancer Treatment Study",
                Description = "A phase III study for cancer treatment",
                Phase = "Phase III",
                Status = "Active",
                Sponsor = "Pharma Corp",
                PrimaryEndpoint = "Overall Survival",
                SecondaryEndpoints = "Progression-free survival",
                TargetEnrollment = 500,
                CurrentEnrollment = 250,
                StartDate = DateTime.UtcNow.AddMonths(-6),
                EstimatedCompletionDate = DateTime.UtcNow.AddMonths(18),
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddMonths(-7)
            }
        };

        var clinicalTrialDtos = new List<ClinicalTrialComparatorDto>
        {
            new ClinicalTrialComparatorDto
            {
                Id = 1,
                TrialId = "NCT12345678",
                Title = "Cancer Treatment Study",
                Description = "A phase III study for cancer treatment",
                Phase = "Phase III",
                Status = "Active",
                Sponsor = "Pharma Corp"
            }
        };

        var totalCount = 1;

        _repositoryMock
            .Setup(x => x.GetClinicalTrialsPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.PhaseFilter,
                request.SponsorFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((clinicalTrials, totalCount));

        _mapperMock
            .Setup(x => x.Map<IEnumerable<ClinicalTrialComparatorDto>>(clinicalTrials))
            .Returns(clinicalTrialDtos);

        // Act
        var result = await _service.GetClinicalTrialsPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(1);
        result.Data.TotalCount.Should().Be(1);
        result.Message.Should().Be("Clinical trials retrieved successfully");
    }

    [Fact]
    public async Task GetClinicalTrialsPagedAsync_EmptyResult_ReturnsSuccessWithEmptyData()
    {
        // Arrange
        var request = new ClinicalTrialComparatorSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "NonExistentTrial"
        };

        var clinicalTrials = new List<ClinicalTrialComparator>();
        var clinicalTrialDtos = new List<ClinicalTrialComparatorDto>();
        var totalCount = 0;

        _repositoryMock
            .Setup(x => x.GetClinicalTrialsPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.PhaseFilter,
                request.SponsorFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((clinicalTrials, totalCount));

        _mapperMock
            .Setup(x => x.Map<IEnumerable<ClinicalTrialComparatorDto>>(clinicalTrials))
            .Returns(clinicalTrialDtos);

        // Act
        var result = await _service.GetClinicalTrialsPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().BeEmpty();
        result.Data.TotalCount.Should().Be(0);
        result.Message.Should().Be("Clinical trials retrieved successfully");
    }

    [Fact]
    public async Task GetClinicalTrialsPagedAsync_RepositoryThrowsException_ReturnsErrorResponse()
    {
        // Arrange
        var request = new ClinicalTrialComparatorSearchRequest
        {
            PageNumber = 1,
            PageSize = 10
        };

        var exception = new InvalidOperationException("Database connection failed");

        _repositoryMock
            .Setup(x => x.GetClinicalTrialsPagedAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _service.GetClinicalTrialsPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Data.Should().BeNull();
        result.Error.Should().NotBeNull();
        result.Error!.Message.Should().Be("An error occurred while retrieving clinical trials");
    }

    [Fact]
    public async Task GetFiltersAsync_ValidCall_ReturnsSuccessResponse()
    {
        // Act
        var result = await _service.GetFiltersAsync();

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Message.Should().Be("Filter options retrieved successfully");
    }

    [Fact]
    public async Task GetStatsAsync_ValidCall_ReturnsSuccessResponse()
    {
        // Act
        var result = await _service.GetStatsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Message.Should().Be("Statistics retrieved successfully");
    }

    [Fact]
    public async Task GetClinicalTrialsPagedAsync_NullRequest_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _service.GetClinicalTrialsPagedAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullRepository_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new ClinicalTrialComparatorService(null!, _loggerMock.Object, _mapperMock.Object);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullLogger_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new ClinicalTrialComparatorService(_repositoryMock.Object, null!, _mapperMock.Object);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullMapper_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new ClinicalTrialComparatorService(_repositoryMock.Object, _loggerMock.Object, null!);
        act.Should().Throw<ArgumentNullException>();
    }
}
