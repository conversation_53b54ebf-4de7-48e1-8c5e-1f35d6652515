using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using GDAPI.Modules.UserManagement.Application.Services;
using GDAPI.Modules.UserManagement.Domain.Interfaces;
using GDAPI.Modules.UserManagement.Domain.Entities;
using GDAPI.Modules.UserManagement.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.UnitTests.Modules.UserManagement.Tests;

public class UserServiceTests
{
    private readonly Mock<IUserRepository> _repositoryMock;
    private readonly Mock<ILogger<UserService>> _loggerMock;
    private readonly UserService _service;

    public UserServiceTests()
    {
        _repositoryMock = new Mock<IUserRepository>();
        _loggerMock = new Mock<ILogger<UserService>>();
        _service = new UserService(_repositoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GetUsersPagedAsync_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new UserSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "John",
            StatusFilter = "Active",
            RoleFilter = "Admin",
            DepartmentFilter = "IT"
        };

        var users = new List<User>
        {
            new User
            {
                Id = 1,
                Username = "john.doe",
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                FullName = "John Doe",
                Role = "Admin",
                Department = "IT",
                Status = "Active",
                PhoneNumber = "+1234567890",
                JobTitle = "System Administrator",
                ManagerId = 2,
                HireDate = DateTime.UtcNow.AddYears(-2),
                LastLoginDate = DateTime.UtcNow.AddDays(-1),
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddYears(-2),
                CreatedBy = "system"
            }
        };

        var totalCount = 1;

        _repositoryMock
            .Setup(x => x.GetUsersPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.RoleFilter,
                request.DepartmentFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((users, totalCount));

        // Act
        var result = await _service.GetUsersPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(1);
        result.Data.TotalCount.Should().Be(1);
        result.Message.Should().Be("Users retrieved successfully");
    }

    [Fact]
    public async Task GetUsersPagedAsync_EmptyResult_ReturnsSuccessWithEmptyData()
    {
        // Arrange
        var request = new UserSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "NonExistentUser"
        };

        var users = new List<User>();
        var totalCount = 0;

        _repositoryMock
            .Setup(x => x.GetUsersPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.RoleFilter,
                request.DepartmentFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((users, totalCount));

        // Act
        var result = await _service.GetUsersPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().BeEmpty();
        result.Data.TotalCount.Should().Be(0);
        result.Message.Should().Be("Users retrieved successfully");
    }

    [Fact]
    public async Task GetUsersPagedAsync_RepositoryThrowsException_ReturnsErrorResponse()
    {
        // Arrange
        var request = new UserSearchRequest
        {
            PageNumber = 1,
            PageSize = 10
        };

        var exception = new InvalidOperationException("Database connection failed");

        _repositoryMock
            .Setup(x => x.GetUsersPagedAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _service.GetUsersPagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Data.Should().BeNull();
        result.Error.Should().NotBeNull();
        result.Error!.Message.Should().Be("An error occurred while retrieving users");
    }

    [Fact]
    public async Task GetUserByIdAsync_ValidId_ReturnsSuccessResponse()
    {
        // Arrange
        var userId = 1;
        var user = new User
        {
            Id = userId,
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            FullName = "John Doe",
            Role = "Admin",
            Department = "IT",
            Status = "Active",
            IsActive = true
        };

        _repositoryMock
            .Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _service.GetUserByIdAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Id.Should().Be(userId);
        result.Message.Should().Be("User retrieved successfully");
    }

    [Fact]
    public async Task GetUserByIdAsync_UserNotFound_ReturnsErrorResponse()
    {
        // Arrange
        var userId = 999;

        _repositoryMock
            .Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _service.GetUserByIdAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Data.Should().BeNull();
        result.Error.Should().NotBeNull();
        result.Error!.Message.Should().Be("User not found");
    }

    [Fact]
    public async Task CreateUserAsync_ValidUser_ReturnsSuccessResponse()
    {
        // Arrange
        var createRequest = new CreateUserRequest
        {
            Username = "jane.smith",
            Email = "<EMAIL>",
            FirstName = "Jane",
            LastName = "Smith",
            Role = "User",
            Department = "HR",
            PhoneNumber = "+1234567891",
            JobTitle = "HR Specialist",
            ManagerId = 1
        };

        var createdUser = new User
        {
            Id = 2,
            Username = createRequest.Username,
            Email = createRequest.Email,
            FirstName = createRequest.FirstName,
            LastName = createRequest.LastName,
            FullName = $"{createRequest.FirstName} {createRequest.LastName}",
            Role = createRequest.Role,
            Department = createRequest.Department,
            Status = "Active",
            PhoneNumber = createRequest.PhoneNumber,
            JobTitle = createRequest.JobTitle,
            ManagerId = createRequest.ManagerId,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _repositoryMock
            .Setup(x => x.CreateUserAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdUser);

        // Act
        var result = await _service.CreateUserAsync(createRequest);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Username.Should().Be(createRequest.Username);
        result.Message.Should().Be("User created successfully");
    }

    [Fact]
    public async Task UpdateUserAsync_ValidUser_ReturnsSuccessResponse()
    {
        // Arrange
        var userId = 1;
        var updateRequest = new UpdateUserRequest
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = "Senior Admin",
            Department = "IT",
            PhoneNumber = "+1234567890",
            JobTitle = "Senior System Administrator",
            ManagerId = 2
        };

        var existingUser = new User
        {
            Id = userId,
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            IsActive = true
        };

        var updatedUser = new User
        {
            Id = userId,
            Username = existingUser.Username,
            Email = updateRequest.Email,
            FirstName = updateRequest.FirstName,
            LastName = updateRequest.LastName,
            FullName = $"{updateRequest.FirstName} {updateRequest.LastName}",
            Role = updateRequest.Role,
            Department = updateRequest.Department,
            PhoneNumber = updateRequest.PhoneNumber,
            JobTitle = updateRequest.JobTitle,
            ManagerId = updateRequest.ManagerId,
            IsActive = true,
            UpdatedAt = DateTime.UtcNow
        };

        _repositoryMock
            .Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingUser);

        _repositoryMock
            .Setup(x => x.UpdateUserAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedUser);

        // Act
        var result = await _service.UpdateUserAsync(userId, updateRequest);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Email.Should().Be(updateRequest.Email);
        result.Message.Should().Be("User updated successfully");
    }

    [Fact]
    public async Task DeleteUserAsync_ValidId_ReturnsSuccessResponse()
    {
        // Arrange
        var userId = 1;
        var existingUser = new User
        {
            Id = userId,
            Username = "john.doe",
            IsActive = true
        };

        _repositoryMock
            .Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingUser);

        _repositoryMock
            .Setup(x => x.DeleteUserAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.DeleteUserAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Message.Should().Be("User deleted successfully");
    }

    [Fact]
    public async Task GetUsersPagedAsync_NullRequest_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _service.GetUsersPagedAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullRepository_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new UserService(null!, _loggerMock.Object);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullLogger_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new UserService(_repositoryMock.Object, null!);
        act.Should().Throw<ArgumentNullException>();
    }
}
