using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using System.Data;
using GDAPI.Modules.UserManagement.Infrastructure.Repositories;
using GDAPI.Modules.UserManagement.Domain.Interfaces;
using GDAPI.Modules.UserManagement.Domain.Entities;

namespace GDAPI.UnitTests.Modules.UserManagement.Tests;

public class UserRepositoryTests
{
    private readonly Mock<IUserManagementDbConnectionFactory> _connectionFactoryMock;
    private readonly Mock<ILogger<UserRepository>> _loggerMock;
    private readonly Mock<IDbConnection> _connectionMock;
    private readonly UserRepository _repository;

    public UserRepositoryTests()
    {
        _connectionFactoryMock = new Mock<IUserManagementDbConnectionFactory>();
        _loggerMock = new Mock<ILogger<UserRepository>>();
        _connectionMock = new Mock<IDbConnection>();
        
        _connectionFactoryMock
            .Setup(x => x.CreateConnection())
            .Returns(_connectionMock.Object);

        _repository = new UserRepository(_connectionFactoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public void Constructor_ValidParameters_CreatesInstance()
    {
        // Act & Assert
        _repository.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_NullConnectionFactory_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new UserRepository(null!, _loggerMock.Object);
        act.Should().Throw<ArgumentNullException>()
           .WithParameterName("connectionFactory");
    }

    [Fact]
    public void Constructor_NullLogger_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new UserRepository(_connectionFactoryMock.Object, null!);
        act.Should().Throw<ArgumentNullException>()
           .WithParameterName("logger");
    }

    [Fact]
    public async Task GetUsersPagedAsync_ValidParameters_CallsConnectionFactory()
    {
        // Arrange
        var pageNumber = 1;
        var pageSize = 10;
        var searchTerm = "john";
        var statusFilter = "Active";
        var roleFilter = "Admin";
        var departmentFilter = "IT";
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.GetUsersPagedAsync(
                pageNumber, 
                pageSize, 
                searchTerm, 
                statusFilter, 
                roleFilter, 
                departmentFilter, 
                cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing the setup
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Theory]
    [InlineData(0, 10)]
    [InlineData(-1, 10)]
    [InlineData(1, 0)]
    [InlineData(1, -1)]
    public async Task GetUsersPagedAsync_InvalidPagination_ThrowsArgumentException(int pageNumber, int pageSize)
    {
        // Act & Assert
        var act = async () => await _repository.GetUsersPagedAsync(
            pageNumber, 
            pageSize, 
            null, 
            null, 
            null, 
            null, 
            CancellationToken.None);
        
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task GetUsersPagedAsync_CancellationRequested_ThrowsOperationCanceledException()
    {
        // Arrange
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        var act = async () => await _repository.GetUsersPagedAsync(
            1, 
            10, 
            null, 
            null, 
            null, 
            null, 
            cancellationTokenSource.Token);
        
        await act.Should().ThrowAsync<OperationCanceledException>();
    }

    [Fact]
    public async Task GetUserByIdAsync_ValidId_CallsConnectionFactory()
    {
        // Arrange
        var userId = 1;
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.GetUserByIdAsync(userId, cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing the setup
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public async Task GetUserByIdAsync_InvalidId_ThrowsArgumentException(int userId)
    {
        // Act & Assert
        var act = async () => await _repository.GetUserByIdAsync(userId, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task CreateUserAsync_ValidUser_CallsConnectionFactory()
    {
        // Arrange
        var user = new User
        {
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = "User",
            Department = "IT",
            IsActive = true
        };
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.CreateUserAsync(user, cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing the setup
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Fact]
    public async Task CreateUserAsync_NullUser_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _repository.CreateUserAsync(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task UpdateUserAsync_ValidUser_CallsConnectionFactory()
    {
        // Arrange
        var user = new User
        {
            Id = 1,
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = "User",
            Department = "IT",
            IsActive = true
        };
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.UpdateUserAsync(user, cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing the setup
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Fact]
    public async Task UpdateUserAsync_NullUser_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _repository.UpdateUserAsync(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task UpdateUserAsync_UserWithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var user = new User
        {
            Id = 0, // Invalid ID
            Username = "john.doe",
            Email = "<EMAIL>"
        };

        // Act & Assert
        var act = async () => await _repository.UpdateUserAsync(user, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task DeleteUserAsync_ValidId_CallsConnectionFactory()
    {
        // Arrange
        var userId = 1;
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.DeleteUserAsync(userId, cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing the setup
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public async Task DeleteUserAsync_InvalidId_ThrowsArgumentException(int userId)
    {
        // Act & Assert
        var act = async () => await _repository.DeleteUserAsync(userId, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task GetUsersPagedAsync_NullSearchTerm_HandlesNullValue()
    {
        // Arrange
        var pageNumber = 1;
        var pageSize = 10;
        string? searchTerm = null;
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.GetUsersPagedAsync(
                pageNumber, 
                pageSize, 
                searchTerm, 
                null, 
                null, 
                null, 
                cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing parameter handling
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Fact]
    public async Task GetUsersPagedAsync_EmptyFilters_HandlesEmptyValues()
    {
        // Arrange
        var pageNumber = 1;
        var pageSize = 10;
        var searchTerm = "";
        var statusFilter = "";
        var roleFilter = "";
        var departmentFilter = "";
        var cancellationToken = CancellationToken.None;

        // Act
        try
        {
            await _repository.GetUsersPagedAsync(
                pageNumber, 
                pageSize, 
                searchTerm, 
                statusFilter, 
                roleFilter, 
                departmentFilter, 
                cancellationToken);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing parameter handling
        }

        // Assert
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Theory]
    [InlineData(1, 1)]
    [InlineData(1, 100)]
    [InlineData(10, 50)]
    [InlineData(100, 10)]
    public async Task GetUsersPagedAsync_ValidPaginationParameters_AcceptsValidValues(int pageNumber, int pageSize)
    {
        // Act
        try
        {
            await _repository.GetUsersPagedAsync(
                pageNumber, 
                pageSize, 
                null, 
                null, 
                null, 
                null, 
                CancellationToken.None);
        }
        catch
        {
            // Expected to fail due to mocked connection, but we're testing parameter validation
        }

        // Assert - No exception should be thrown for valid parameters
        _connectionFactoryMock.Verify(x => x.CreateConnection(), Times.Once);
    }

    [Fact]
    public void Dispose_WhenCalled_DisposesResources()
    {
        // Act
        _repository.Dispose();

        // Assert - Should not throw any exceptions
        // The actual disposal logic would be tested with integration tests
        // since we're mocking the dependencies here
    }
}
