import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TreePine } from 'lucide-react';

interface ForestPlotProps {
  data: any[];
  title: string;
  config: {};
}

export const ForestPlot: React.FC<ForestPlotProps> = ({ data, title, config }) => {
  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg flex items-center justify-center border-2 border-dashed border-green-300">
        <div className="text-center">
          <TreePine className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <p className="text-green-600 font-medium text-lg">Hazard Plot</p>
          <p className="text-sm text-green-500 mt-2">No data available with current filter selection</p>
          <p className="text-xs text-green-400 mt-1">Charting Requirements: Single endpoint, &lt;10 assets recommended, Multiple timepoints</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Hazard plot risk analysis</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg flex items-center justify-center border-2 border-dashed border-green-300">
          <div className="text-center">
            <TreePine className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <p className="text-green-600 font-medium text-lg">Hazard Plot</p>
            <p className="text-sm text-green-500 mt-2">Chart implementation pending</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};