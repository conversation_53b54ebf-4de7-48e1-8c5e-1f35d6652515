using System.ComponentModel.DataAnnotations;

namespace GDAPI.Modules.AuthoringTool.Domain.Entities;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsDeleted { get; set; } = false;
    
    /// <summary>
    /// Row version for optimistic concurrency control
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }
}

/// <summary>
/// Authoring entity representing authoring items in the authoring tool system
/// </summary>
public class Authoring : BaseEntity
{
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    [StringLength(50)]
    public string AuthoringType { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string? Category { get; set; }
    
    [StringLength(500)]
    public string? Tags { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Author { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string? CoAuthors { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Status { get; set; } = "Draft";
    
    [StringLength(20)]
    public string? Version { get; set; }
    
    [StringLength(500)]
    public string? FilePath { get; set; }
    
    [StringLength(100)]
    public string? FileName { get; set; }
    
    public long? FileSize { get; set; }
    
    [StringLength(50)]
    public string? FileFormat { get; set; }
    
    public DateTime? LastModified { get; set; }
    
    public DateTime? PublishedDate { get; set; }
    
    [StringLength(100)]
    public string? Department { get; set; }
    
    [StringLength(100)]
    public string? Project { get; set; }
    
    [StringLength(50)]
    public string? Priority { get; set; }
    
    public DateTime? DueDate { get; set; }
    
    [StringLength(1000)]
    public string? ReviewComments { get; set; }
    
    [StringLength(100)]
    public string? ReviewedBy { get; set; }
    
    public DateTime? ReviewedDate { get; set; }
    
    [StringLength(100)]
    public string? ApprovedBy { get; set; }
    
    public DateTime? ApprovedDate { get; set; }
    
    // Computed properties for display
    public string StatusDisplay => Status switch
    {
        "Draft" => "📝 Draft",
        "InReview" => "👀 In Review",
        "Approved" => "✅ Approved",
        "Published" => "🚀 Published",
        "Archived" => "📦 Archived",
        _ => Status
    };
    
    public string AuthorInfo => !string.IsNullOrEmpty(CoAuthors) 
        ? $"{Author} (with {CoAuthors})" 
        : Author;
    
    public string FileSizeDisplay => FileSize.HasValue 
        ? FileSize.Value switch
        {
            < 1024 => $"{FileSize.Value} B",
            < 1024 * 1024 => $"{FileSize.Value / 1024:F1} KB",
            < 1024 * 1024 * 1024 => $"{FileSize.Value / (1024 * 1024):F1} MB",
            _ => $"{FileSize.Value / (1024 * 1024 * 1024):F1} GB"
        }
        : "N/A";
    
    public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow && Status != "Published" && Status != "Archived";
    
    public string PriorityDisplay => Priority switch
    {
        "High" => "🔴 High",
        "Medium" => "🟡 Medium",
        "Low" => "🟢 Low",
        _ => Priority ?? "N/A"
    };
}


#region : Get User Details 

// Added by: Avinash Veerella
// Date: 16-Jul-2025
// Description: Retrieves user details Model.

public class UserDetails
{
    public int Id { get; set; }
    public string ShortCode { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string EmailAddress { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string LastLogin { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

#endregion

#region : 

public class ClinicalComparatorsGroupByIndications
{
    public string IndicationName { get; set; } = string.Empty;
    public List<ClinicalComparatorsEntry> Entries { get; set; } = new List<ClinicalComparatorsEntry>();
}

public class ClinicalComparatorsEntry
{
    public int Id { get; set; }
    public string VersionName { get; set; } = string.Empty;
    public ClinicalComparatorsStatus Status { get; set; }
    public bool IsDraftUpdate { get; set; }
    public string AccessType { get; set; } = string.Empty;
    public bool IsAccessTypeFirewalled { get; set; }
    public string OwnerInitials { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public string LastModifiedDisplay { get; set; } = string.Empty; // Using a string for display simplicity
    public string? ClientName { get; set; }
    public bool IsClientFirewalled { get; set; }
}

public enum ClinicalComparatorsStatus
{
    Draft,
    Published
}

public class ClinicalComparatorsData
{
    public int Id { get; set; }
    public string IndicationName { get; set; }
    public string VersionName { get; set; }
    public string PublishStatus { get; set; }
    public bool IsDraftUpdate { get; set; } // This comes from our updated SP
    public string AccessType { get; set; }
    public string ShortCode { get; set; }
    public string DisplayName { get; set; }
    public string ClientName { get; set; } // This also comes from our updated SP
    public string LastModified { get; set; }
    public string Status { get; set; }
}

#endregion