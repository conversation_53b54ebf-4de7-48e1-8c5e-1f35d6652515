
const fs = require('fs');
const path = require('path');

// Function to parse tab-delimited data
function parseTabDelimitedData(filePath) {
  const data = fs.readFileSync(filePath, 'utf8');
  const lines = data.trim().split('\n');
  
  if (lines.length === 0) return [];
  
  // Get headers from first line
  const headers = lines[0].split('\t');
  
  // Parse data rows
  const parsedData = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split('\t');
    const row = {};
    
    headers.forEach((header, index) => {
      // Clean up header names (remove special characters, convert to camelCase)
      const cleanHeader = header.toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');
      
      // Convert values to appropriate types
      let value = values[index] || '';
      
      // Try to convert to number if it looks like a number
      if (value && !isNaN(value) && value !== '') {
        value = parseFloat(value);
      }
      
      // Convert boolean-like strings
      if (value === 'TRUE' || value === 'FALSE') {
        value = value === 'TRUE';
      }
      
      row[cleanHeader] = value;
    });
    
    parsedData.push(row);
  }
  
  return parsedData;
}

// Function to build filter indexes
function buildFilterIndexes(data) {
  const indexes = {};
  
  if (data.length === 0) return indexes;
  
  // Get all unique keys from the data
  const allKeys = new Set();
  data.forEach(row => {
    Object.keys(row).forEach(key => allKeys.add(key));
  });
  
  // Build indexes for each field
  allKeys.forEach(key => {
    indexes[key] = new Set();
    data.forEach(row => {
      if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
        indexes[key].add(row[key]);
      }
    });
  });
  
  return indexes;
}

// Function to generate JavaScript file content
function generateJavaScriptFile(data, indexes, dataType) {
  const filterFunctionName = dataType === 'safety' ? 'filterSafetyData' : 'filterEfficacyData';
  const optionsFunctionName = dataType === 'safety' ? 'getSafetyFilterOptions' : 'getEfficacyFilterOptions';
  const dataVariableName = dataType === 'safety' ? 'safetyData' : 'efficacyData';
  const indexesVariableName = dataType === 'safety' ? 'safetyFilterIndexes' : 'efficacyFilterIndexes';
  
  let content = `// Preprocessed ${dataType} data for optimal performance\n`;
  content += `// Generated from data/comparator/raw/${dataType}-ibd.txt\n\n`;
  
  // Export data array
  content += `export const ${dataVariableName} = ${JSON.stringify(data, null, 2)};\n\n`;
  
  // Export filter indexes (convert Sets to Arrays for JSON serialization)
  const serializableIndexes = {};
  Object.keys(indexes).forEach(key => {
    serializableIndexes[key] = Array.from(indexes[key]).sort();
  });
  
  content += `// Filter indexes for fast lookup\n`;
  content += `export const ${indexesVariableName} = {\n`;
  Object.keys(serializableIndexes).forEach(key => {
    content += `  ${key}: new Set(${JSON.stringify(serializableIndexes[key])}),\n`;
  });
  content += `};\n\n`;
  
  // Export filter options function
  content += `// Function to get unique filter values\n`;
  content += `export const ${optionsFunctionName} = () => {\n`;
  content += `  return {\n`;
  Object.keys(serializableIndexes).forEach(key => {
    content += `    ${key}: Array.from(${indexesVariableName}.${key}).sort(),\n`;
  });
  content += `  };\n`;
  content += `};\n\n`;
  
  // Export filter function
  content += `// Function to filter ${dataType} data based on selected filters\n`;
  content += `export const ${filterFunctionName} = (filters) => {\n`;
  content += `  return ${dataVariableName}.filter(row => {\n`;
  content += `    return Object.entries(filters).every(([key, values]) => {\n`;
  content += `      if (!values || values.length === 0) return true;\n`;
  content += `      return values.includes(row[key]);\n`;
  content += `    });\n`;
  content += `  });\n`;
  content += `};\n`;
  
  return content;
}

// Main processing function
function processData() {
  console.log('Processing safety and efficacy data...');
  
  // Process safety data
  const safetyRawPath = path.join(__dirname, '../data/comparator/raw/safety-ibd.txt');
  const safetyOutputPath = path.join(__dirname, '../data/comparator/safety-data.js');
  
  if (fs.existsSync(safetyRawPath)) {
    console.log('Processing safety data...');
    const safetyData = parseTabDelimitedData(safetyRawPath);
    const safetyIndexes = buildFilterIndexes(safetyData);
    const safetyContent = generateJavaScriptFile(safetyData, safetyIndexes, 'safety');
    
    fs.writeFileSync(safetyOutputPath, safetyContent);
    console.log(`✓ Safety data processed: ${safetyData.length} records`);
  } else {
    console.log('⚠ Safety data file not found:', safetyRawPath);
  }
  
  // Process efficacy data
  const efficacyRawPath = path.join(__dirname, '../data/comparator/raw/efficacy-ibd.txt');
  const efficacyOutputPath = path.join(__dirname, '../data/comparator/efficacy-data.js');
  
  if (fs.existsSync(efficacyRawPath)) {
    console.log('Processing efficacy data...');
    const efficacyData = parseTabDelimitedData(efficacyRawPath);
    const efficacyIndexes = buildFilterIndexes(efficacyData);
    const efficacyContent = generateJavaScriptFile(efficacyData, efficacyIndexes, 'efficacy');
    
    fs.writeFileSync(efficacyOutputPath, efficacyContent);
    console.log(`✓ Efficacy data processed: ${efficacyData.length} records`);
  } else {
    console.log('⚠ Efficacy data file not found:', efficacyRawPath);
  }
  
  console.log('Data processing complete!');
}

// Run the processing
processData();
