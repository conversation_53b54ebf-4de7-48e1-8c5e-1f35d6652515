import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Grid3X3 } from 'lucide-react';

interface SafetyEventMatrixProps {
  data: any[];
  title: string;
  config?: {
    sortOrder: 'severity' | 'frequency' | 'alphabetical';
  };
}

export const SafetyEventMatrix: React.FC<SafetyEventMatrixProps> = ({ data, title, config }) => {
  const uniqueAssets = [...new Set(data.map(item => item.asset))];

  // Check display requirements
  if (uniqueAssets.length >= 10) {
    return (
      <div className="h-80 bg-gradient-to-br from-red-50 to-pink-100 rounded-lg flex items-center justify-center border-2 border-dashed border-red-300">
        <div className="text-center">
          <Grid3X3 className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <p className="text-red-600 font-medium text-lg">Safety Event Matrix</p>
          <p className="text-sm text-red-500 mt-2">Too many comparators to display effectively</p>
          <p className="text-xs text-red-400 mt-1">Please select fewer than 10 drugs for this visualization</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-red-50 to-pink-100 rounded-lg flex items-center justify-center border-2 border-dashed border-red-300">
        <div className="text-center">
          <Grid3X3 className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <p className="text-red-600 font-medium text-lg">Safety Event Matrix</p>
          <p className="text-sm text-red-500 mt-2">No data available with current filter selection</p>
          <p className="text-xs text-red-400 mt-1">Charting Requirements: &lt; 10 comparators, Single timepoint preferred</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Safety event matrix visualization</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-red-50 to-pink-100 rounded-lg flex items-center justify-center border-2 border-dashed border-red-300">
          <div className="text-center">
            <Grid3X3 className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <p className="text-red-600 font-medium text-lg">Safety Event Matrix</p>
            <p className="text-sm text-red-500 mt-2">Chart implementation pending</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};