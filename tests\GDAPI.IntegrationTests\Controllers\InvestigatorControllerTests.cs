using Xunit;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net;
using GDAPI.IntegrationTests.Infrastructure;
using GDAPI.Modules.ProductManagement.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.IntegrationTests.Controllers;

public class InvestigatorControllerTests : IntegrationTestBase
{
    public InvestigatorControllerTests(WebApplicationFactory<Program> factory) : base(factory)
    {
    }

    [Fact]
    public async Task GetInvestigators_WithDefaultParameters_ReturnsSuccessResponse()
    {
        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>("/api/investigators");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        response.Data!.Items.Should().NotBeNull();
        response.Data.PageNumber.Should().Be(1);
        response.Data.PageSize.Should().BeGreaterThan(0);
        response.Data.TotalCount.Should().BeGreaterOrEqualTo(0);
        response.Message.Should().Be("Investigators retrieved successfully");
    }

    [Fact]
    public async Task GetInvestigators_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var pageNumber = 1;
        var pageSize = 5;

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?pageNumber={pageNumber}&pageSize={pageSize}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        response.Data!.PageNumber.Should().Be(pageNumber);
        response.Data.PageSize.Should().Be(pageSize);
        response.Data.Items.Should().HaveCountLessOrEqualTo(pageSize);
    }

    [Fact]
    public async Task GetInvestigators_WithSearchTerm_FiltersResults()
    {
        // Arrange
        var searchTerm = "Dr";

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?searchTerm={searchTerm}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        
        if (response.Data!.Items.Any())
        {
            response.Data.Items.Should().OnlyContain(i => 
                i.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                i.SpecializationNames?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                i.Organisation?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true);
        }
    }

    [Fact]
    public async Task GetInvestigators_WithRegionFilter_FiltersResults()
    {
        // Arrange
        var regionFilter = "North America";

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?regionFilter={Uri.EscapeDataString(regionFilter)}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        
        if (response.Data!.Items.Any())
        {
            response.Data.Items.Should().OnlyContain(i => 
                i.RegionName?.Equals(regionFilter, StringComparison.OrdinalIgnoreCase) == true);
        }
    }

    [Fact]
    public async Task GetInvestigators_WithCountryFilter_FiltersResults()
    {
        // Arrange
        var countryFilter = "USA";

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?countryFilter={countryFilter}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        
        if (response.Data!.Items.Any())
        {
            response.Data.Items.Should().OnlyContain(i => 
                i.CountryName?.Equals(countryFilter, StringComparison.OrdinalIgnoreCase) == true);
        }
    }

    [Fact]
    public async Task GetInvestigators_WithMultipleFilters_AppliesAllFilters()
    {
        // Arrange
        var searchTerm = "Dr";
        var regionFilter = "North America";
        var countryFilter = "USA";

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?searchTerm={searchTerm}&regionFilter={Uri.EscapeDataString(regionFilter)}&countryFilter={countryFilter}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        
        if (response.Data!.Items.Any())
        {
            foreach (var investigator in response.Data.Items)
            {
                // Should match search term
                (investigator.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 investigator.SpecializationNames?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                 investigator.Organisation?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                .Should().BeTrue();

                // Should match region filter
                investigator.RegionName?.Equals(regionFilter, StringComparison.OrdinalIgnoreCase).Should().BeTrue();

                // Should match country filter
                investigator.CountryName?.Equals(countryFilter, StringComparison.OrdinalIgnoreCase).Should().BeTrue();
            }
        }
    }

    [Theory]
    [InlineData(0, 10)]
    [InlineData(-1, 10)]
    [InlineData(1, 0)]
    [InlineData(1, -1)]
    [InlineData(1, 1001)]
    public async Task GetInvestigators_WithInvalidPagination_ReturnsBadRequest(int pageNumber, int pageSize)
    {
        // Act
        var httpResponse = await Client.GetAsync($"/api/investigators?pageNumber={pageNumber}&pageSize={pageSize}");

        // Assert
        httpResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetInvestigators_WithoutApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var unauthenticatedClient = CreateUnauthenticatedClient();

        // Act
        var response = await unauthenticatedClient.GetAsync("/api/investigators");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task GetInvestigators_WithInvalidApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var clientWithInvalidKey = CreateClientWithApiKey("invalid-api-key");

        // Act
        var response = await clientWithInvalidKey.GetAsync("/api/investigators");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task GetInvestigators_ResponseStructure_IsCorrect()
    {
        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>("/api/investigators?pageSize=1");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        response.Data!.Items.Should().NotBeNull();
        response.Data.PageNumber.Should().BeGreaterThan(0);
        response.Data.PageSize.Should().BeGreaterThan(0);
        response.Data.TotalCount.Should().BeGreaterOrEqualTo(0);
        response.Data.TotalPages.Should().BeGreaterOrEqualTo(0);
        response.Message.Should().NotBeNullOrEmpty();
        response.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));

        if (response.Data.Items.Any())
        {
            var investigator = response.Data.Items.First();
            investigator.Id.Should().BeGreaterThan(0);
            investigator.InvestigatorName.Should().NotBeNullOrEmpty();
            // Other properties can be null but should be properly typed
        }
    }

    [Fact]
    public async Task GetInvestigators_WithLargePageSize_ReturnsLimitedResults()
    {
        // Arrange
        var largePageSize = 500; // Assuming max allowed is less than this

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>(
            $"/api/investigators?pageSize={largePageSize}");

        // Assert
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
        response.Data.Should().NotBeNull();
        
        // Should respect maximum page size limit
        response.Data!.Items.Should().HaveCountLessOrEqualTo(100); // Assuming max is 100
    }

    [Fact]
    public async Task GetInvestigators_ConcurrentRequests_HandlesConcurrency()
    {
        // Arrange
        var tasks = new List<Task<ApiResponse<PaginatedResult<InvestigatorDto>>?>>();

        // Act - Make 5 concurrent requests
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(GetApiResponseAsync<PaginatedResult<InvestigatorDto>>("/api/investigators"));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().HaveCount(5);
        responses.Should().OnlyContain(r => r != null && r.Success);
        
        foreach (var response in responses)
        {
            response!.Data.Should().NotBeNull();
            response.Data!.Items.Should().NotBeNull();
        }
    }

    [Fact]
    public async Task GetInvestigators_PerformanceTest_CompletesWithinReasonableTime()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await GetApiResponseAsync<PaginatedResult<InvestigatorDto>>("/api/investigators");

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
        
        response.Should().NotBeNull();
        response!.Success.Should().BeTrue();
    }
}
