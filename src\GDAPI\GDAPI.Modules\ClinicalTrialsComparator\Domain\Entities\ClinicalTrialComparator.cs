using System.ComponentModel.DataAnnotations;

namespace GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsDeleted { get; set; } = false;
    
    /// <summary>
    /// Row version for optimistic concurrency control
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }
}

/// <summary>
/// Clinical Trial Comparator entity representing clinical trial comparison data
/// </summary>
public class ClinicalTrialComparator : BaseEntity
{
    [StringLength(100)]
    public string? Asset { get; set; }
    
    [StringLength(200)]
    public string? TrialName { get; set; }
    
    [StringLength(50)]
    public string? TrialPhase { get; set; }
    
    [StringLength(200)]
    public string? Indication { get; set; }
    
    [StringLength(200)]
    public string? SubGroup { get; set; }
    
    [StringLength(200)]
    public string? PortionOfStudy { get; set; }
    
    [StringLength(100)]
    public string? Dose { get; set; }
    
    [StringLength(200)]
    public string? DosingRegimen { get; set; }
    
    [StringLength(100)]
    public string? Roa { get; set; }
    
    [StringLength(100)]
    public string? EventType { get; set; }
    
    [StringLength(200)]
    public string? ResultPublication { get; set; }
    
    [StringLength(100)]
    public string? Timepoint { get; set; }
    
    public int? TimepointWeek { get; set; }
    
    public decimal? DrugSafety { get; set; }
    
    public decimal? ControlSafety { get; set; }
    
    public decimal? DifferenceSafety { get; set; }
    
    public int? NDrug { get; set; }
    
    public int? NControl { get; set; }
    
    [StringLength(200)]
    public string? ActiveComparator { get; set; }
    
    [StringLength(100)]
    public string? DataForDiff { get; set; }
    
    [StringLength(100)]
    public string? DataType { get; set; }
    
    public DateTime? DateOfEntry { get; set; }
    
    [StringLength(500)]
    public string? Misc { get; set; }
    
    [StringLength(100)]
    public string? DrugAcronym { get; set; }
    
    [StringLength(200)]
    public string? DrugBrandName { get; set; }
    
    [StringLength(100)]
    public string? MoleculeType { get; set; }
    
    [StringLength(200)]
    public string? MechanismOfAction { get; set; }
    
    [StringLength(200)]
    public string? ProductCompany { get; set; }
    
    [StringLength(50)]
    public string? NctCode { get; set; }
    
    [StringLength(200)]
    public string? PrimaryTreatmentSetting { get; set; }
    
    [StringLength(50)]
    public string? Age { get; set; }
    
    // Computed properties for display
    public string TrialInfo => !string.IsNullOrEmpty(TrialName) && !string.IsNullOrEmpty(TrialPhase) 
        ? $"{TrialName} (Phase {TrialPhase})" 
        : TrialName ?? "N/A";
    
    public string SafetyComparison => DrugSafety.HasValue && ControlSafety.HasValue 
        ? $"Drug: {DrugSafety:F2}% vs Control: {ControlSafety:F2}%" 
        : "N/A";
    
    public string SampleSizeInfo => NDrug.HasValue && NControl.HasValue 
        ? $"Drug: {NDrug} | Control: {NControl}" 
        : "N/A";
    
    public string CompanyDrugInfo => !string.IsNullOrEmpty(ProductCompany) && !string.IsNullOrEmpty(DrugBrandName) 
        ? $"{DrugBrandName} ({ProductCompany})" 
        : DrugBrandName ?? ProductCompany ?? "N/A";
}
