
import type { NextApiRequest, NextApiResponse } from 'next'
import authConfig from '../../config/auth.json'

type Data = {
  success: boolean
  message?: string
}

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' })
  }

  const { password } = req.body

  if (!password) {
    return res.status(400).json({ success: false, message: 'Password is required' })
  }

  // Check password against config
  if (password === authConfig.password) {
    return res.status(200).json({ success: true })
  } else {
    return res.status(401).json({ success: false, message: 'Invalid password' })
  }
}
