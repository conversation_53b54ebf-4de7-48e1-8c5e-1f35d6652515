using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;

namespace GDAPI.Modules.ClinicalTrialsComparator.Domain.Interfaces;

/// <summary>
/// Clinical Trial Comparator repository interface for ClinicalTrialsComparator module
/// </summary>
public interface IClinicalTrialComparatorRepository
{
    /// <summary>
    /// Get clinical trial comparator data using stored procedure GetCITrialResultsComparator_Grid
    /// </summary>
    /// <param name="moleculeType">Molecule type filter</param>
    /// <param name="mechanismOfAction">Mechanism of action filter</param>
    /// <param name="assetName">Asset name filter</param>
    /// <param name="indication">Indication filter</param>
    /// <param name="phase">Phase filter</param>
    /// <param name="trialName">Trial name filter</param>
    /// <param name="umbrellaEndPoints">Umbrella endpoints filter</param>
    /// <param name="endPoints">Endpoints filter</param>
    /// <param name="eTimePoint">E timepoint filter</param>
    /// <param name="dataHandling">Data handling filter</param>
    /// <param name="eventType">Event type filter</param>
    /// <param name="sTimepoint">S timepoint filter</param>
    /// <param name="dataType">Data type filter (default: 'Efficacy')</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of clinical trial comparator data</returns>
    Task<IEnumerable<ClinicalTrialComparator>> GetClinicalTrialComparatorAsync(
        string? moleculeType = null,
        string? mechanismOfAction = null,
        string? assetName = null,
        string? indication = null,
        string? phase = null,
        string? trialName = null,
        string? umbrellaEndPoints = null,
        string? endPoints = null,
        string? eTimePoint = null,
        string? dataHandling = null,
        string? eventType = null,
        string? sTimepoint = null,
        string dataType = "Efficacy",
        CancellationToken cancellationToken = default);
}
