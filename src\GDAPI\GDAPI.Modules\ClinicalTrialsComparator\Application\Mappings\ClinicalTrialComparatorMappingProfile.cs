using AutoMapper;
using GDAPI.Modules.ClinicalTrialsComparator.Domain.Entities;
using GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;

namespace GDAPI.Modules.ClinicalTrialsComparator.Application.Mappings;

/// <summary>
/// AutoMapper profile for ClinicalTrialComparator mappings
/// </summary>
public class ClinicalTrialComparatorMappingProfile : Profile
{
    public ClinicalTrialComparatorMappingProfile()
    {
        // Entity to DTO mapping
        CreateMap<ClinicalTrialComparator, ClinicalTrialComparatorDto>()
            .ForMember(dest => dest.TrialInfo, opt => opt.MapFrom(src => src.TrialInfo))
            .ForMember(dest => dest.SafetyComparison, opt => opt.MapFrom(src => src.SafetyComparison))
            .ForMember(dest => dest.SampleSizeInfo, opt => opt.MapFrom(src => src.SampleSizeInfo))
            .ForMember(dest => dest.CompanyDrugInfo, opt => opt.MapFrom(src => src.CompanyDrugInfo));

        // DTO to Entity mapping (if needed for create/update operations)
        CreateMap<ClinicalTrialComparatorDto, ClinicalTrialComparator>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.RowVersion, opt => opt.Ignore());

        // Search request to stored procedure parameters mapping (if needed)
        CreateMap<ClinicalTrialComparatorSearchRequest, StoredProcedureParameters>();
    }
}

/// <summary>
/// Helper class for stored procedure parameters
/// </summary>
public class StoredProcedureParameters
{
    public string MoleculeType { get; set; } = string.Empty;
    public string MechanismOfAction { get; set; } = string.Empty;
    public string AssetName { get; set; } = string.Empty;
    public string Indication { get; set; } = string.Empty;
    public string Phase { get; set; } = string.Empty;
    public string TrialName { get; set; } = string.Empty;
    public string UmbrellaEndPoints { get; set; } = string.Empty;
    public string EndPoints { get; set; } = string.Empty;
    public string ETimePoint { get; set; } = string.Empty;
    public string DataHandling { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string STimepoint { get; set; } = string.Empty;
    public string DataType { get; set; } = "Efficacy";
}
