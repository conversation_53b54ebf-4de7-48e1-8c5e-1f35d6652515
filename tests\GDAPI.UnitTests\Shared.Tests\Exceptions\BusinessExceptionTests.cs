using Xunit;
using FluentAssertions;
using System.Net;
using GDAPI.Shared.Exceptions;

namespace GDAPI.UnitTests.Shared.Tests.Exceptions;

public class BusinessExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Business rule violation";

        // Act
        var exception = new BusinessException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("BUSINESS_RULE_VIOLATION");
        exception.CorrelationId.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void Constructor_WithMessageAndInnerException_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Business rule violation";
        var innerException = new InvalidOperationException("Inner exception");

        // Act
        var exception = new BusinessException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("BUSINESS_RULE_VIOLATION");
    }

    [Fact]
    public void Constructor_WithMessageAndCorrelationId_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Business rule violation";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new BusinessException(message, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("BUSINESS_RULE_VIOLATION");
    }

    [Fact]
    public void Constructor_WithAllParameters_SetsAllPropertiesCorrectly()
    {
        // Arrange
        var message = "Business rule violation";
        var innerException = new InvalidOperationException("Inner exception");
        var correlationId = "test-correlation-id";

        // Act
        var exception = new BusinessException(message, innerException, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("BUSINESS_RULE_VIOLATION");
    }

    [Fact]
    public void ToString_ReturnsFormattedString()
    {
        // Arrange
        var message = "Business rule violation";
        var exception = new BusinessException(message);

        // Act
        var result = exception.ToString();

        // Assert
        result.Should().Contain(message);
        result.Should().Contain("BUSINESS_RULE_VIOLATION");
        result.Should().Contain(exception.CorrelationId);
    }
}

public class NotFoundExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Resource not found";

        // Act
        var exception = new NotFoundException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.NotFound);
        exception.ErrorCode.Should().Be("RESOURCE_NOT_FOUND");
    }

    [Fact]
    public void Constructor_WithResourceTypeAndId_SetsPropertiesCorrectly()
    {
        // Arrange
        var resourceType = "User";
        var resourceId = "123";

        // Act
        var exception = new NotFoundException(resourceType, resourceId);

        // Assert
        exception.Message.Should().Be($"{resourceType} with ID '{resourceId}' was not found");
        exception.StatusCode.Should().Be(HttpStatusCode.NotFound);
        exception.ErrorCode.Should().Be("RESOURCE_NOT_FOUND");
    }

    [Fact]
    public void Constructor_WithResourceTypeIdAndCorrelationId_SetsPropertiesCorrectly()
    {
        // Arrange
        var resourceType = "User";
        var resourceId = "123";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new NotFoundException(resourceType, resourceId, correlationId);

        // Assert
        exception.Message.Should().Be($"{resourceType} with ID '{resourceId}' was not found");
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.NotFound);
        exception.ErrorCode.Should().Be("RESOURCE_NOT_FOUND");
    }
}

public class UnauthorizedExceptionTests
{
    [Fact]
    public void Constructor_Default_SetsDefaultMessage()
    {
        // Act
        var exception = new UnauthorizedException();

        // Assert
        exception.Message.Should().Be("Access denied. Authentication required.");
        exception.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        exception.ErrorCode.Should().Be("UNAUTHORIZED_ACCESS");
    }

    [Fact]
    public void Constructor_WithMessage_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Invalid credentials";

        // Act
        var exception = new UnauthorizedException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        exception.ErrorCode.Should().Be("UNAUTHORIZED_ACCESS");
    }

    [Fact]
    public void Constructor_WithMessageAndCorrelationId_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Invalid credentials";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new UnauthorizedException(message, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        exception.ErrorCode.Should().Be("UNAUTHORIZED_ACCESS");
    }
}

public class DatabaseExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Database connection failed";

        // Act
        var exception = new DatabaseException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        exception.ErrorCode.Should().Be("DATABASE_ERROR");
    }

    [Fact]
    public void Constructor_WithMessageAndInnerException_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Database connection failed";
        var innerException = new InvalidOperationException("Connection timeout");

        // Act
        var exception = new DatabaseException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        exception.ErrorCode.Should().Be("DATABASE_ERROR");
    }

    [Fact]
    public void Constructor_WithMessageAndCorrelationId_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Database connection failed";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new DatabaseException(message, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        exception.ErrorCode.Should().Be("DATABASE_ERROR");
    }
}
