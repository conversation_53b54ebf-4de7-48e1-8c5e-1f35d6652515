using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using GDAPI.Shared.Services;

namespace GDAPI.Shared.Middleware;

/// <summary>
/// Extension methods for configuring global exception handling
/// </summary>
public static class ExceptionMiddlewareExtensions
{
    /// <summary>
    /// Add global exception handling services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddGlobalExceptionHandling(this IServiceCollection services)
    {
        // Register error logging service
        services.AddScoped<IErrorLoggingService, ErrorLoggingService>();
        
        return services;
    }

    /// <summary>
    /// Use global exception handling middleware
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder for chaining</returns>
    public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder app)
    {
        return app.UseMiddleware<GlobalExceptionMiddleware>();
    }
}

/// <summary>
/// Extension methods for working with correlation IDs
/// </summary>
public static class CorrelationIdExtensions
{
    /// <summary>
    /// Get the correlation ID from the current HTTP context
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Correlation ID or null if not found</returns>
    public static string? GetCorrelationId(this Microsoft.AspNetCore.Http.HttpContext context)
    {
        // Try to get from headers
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId) && 
            !string.IsNullOrEmpty(correlationId))
        {
            return correlationId;
        }

        // Try to get from response headers
        if (context.Response.Headers.TryGetValue("X-Correlation-ID", out var responseCorrelationId) && 
            !string.IsNullOrEmpty(responseCorrelationId))
        {
            return responseCorrelationId;
        }

        // Use trace identifier as fallback
        return context.TraceIdentifier;
    }

    /// <summary>
    /// Set the correlation ID in the current HTTP context
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <param name="correlationId">Correlation ID to set</param>
    public static void SetCorrelationId(this Microsoft.AspNetCore.Http.HttpContext context, string correlationId)
    {
        if (!context.Response.Headers.ContainsKey("X-Correlation-ID"))
        {
            context.Response.Headers.Add("X-Correlation-ID", correlationId);
        }
    }
}
