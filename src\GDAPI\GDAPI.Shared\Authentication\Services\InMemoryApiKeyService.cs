using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using GDAPI.Shared.Authentication.Models;

namespace GDAPI.Shared.Authentication.Services;

/// <summary>
/// In-memory implementation of API key service for beta/development
/// This will be replaced with database implementation later
/// </summary>
public class InMemoryApiKeyService : IApiKeyService
{
    private readonly ILogger<InMemoryApiKeyService> _logger;
    private readonly ApiKeySettings _settings;
    
    // In-memory storage (will be replaced with database)
    private readonly ConcurrentDictionary<string, ApiKeyInfo> _apiKeys = new();
    private readonly ConcurrentDictionary<string, RateLimitInfo> _rateLimits = new();

    public InMemoryApiKeyService(ILogger<InMemoryApiKeyService> logger, IOptions<ApiKeySettings> settings)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        
        // Initialize with master API key if configured
        InitializeMasterApiKey();
    }

    public async Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey, string? clientIpAddress = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(apiKey))
        {
            return ApiKeyValidationResult.Failure("API key is required");
        }

        // Hash the API key for lookup
        var hashedKey = HashApiKey(apiKey);
        
        if (!_apiKeys.TryGetValue(hashedKey, out var apiKeyInfo))
        {
            _logger.LogWarning("Invalid API key attempted from IP: {IpAddress}", clientIpAddress);
            return ApiKeyValidationResult.Failure("Invalid API key");
        }

        // Check if API key is active
        if (!apiKeyInfo.IsActive)
        {
            _logger.LogWarning("Inactive API key attempted: {KeyName} from IP: {IpAddress}", apiKeyInfo.Name, clientIpAddress);
            return ApiKeyValidationResult.Failure("API key is inactive");
        }

        // Check expiration
        if (apiKeyInfo.ExpiresAt.HasValue && apiKeyInfo.ExpiresAt.Value <= DateTime.UtcNow)
        {
            _logger.LogWarning("Expired API key attempted: {KeyName} from IP: {IpAddress}", apiKeyInfo.Name, clientIpAddress);
            return ApiKeyValidationResult.Failure("API key has expired");
        }

        // Check rate limit if enabled
        if (_settings.EnableRateLimit)
        {
            var rateLimitResult = await CheckRateLimitAsync(hashedKey, apiKeyInfo, clientIpAddress);
            if (!rateLimitResult.IsValid)
            {
                return rateLimitResult;
            }
        }

        // Update last used timestamp
        apiKeyInfo.LastUsedAt = DateTime.UtcNow;

        _logger.LogInformation("API key validated successfully: {KeyName} from IP: {IpAddress}", apiKeyInfo.Name, clientIpAddress);
        
        return ApiKeyValidationResult.Success(apiKeyInfo);
    }

    public Task<string> GenerateApiKeyAsync(string name, string clientName, int? rateLimit = null, DateTime? expiresAt = null, CancellationToken cancellationToken = default)
    {
        // Generate a secure API key
        var apiKey = GenerateSecureApiKey();
        var hashedKey = HashApiKey(apiKey);

        var apiKeyInfo = new ApiKeyInfo
        {
            Id = Guid.NewGuid().ToString(),
            Name = name,
            ClientName = clientName,
            IsActive = true,
            RateLimit = rateLimit ?? _settings.DefaultRateLimit,
            RateLimitWindowMinutes = _settings.RateLimitWindowMinutes,
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };

        _apiKeys[hashedKey] = apiKeyInfo;

        _logger.LogInformation("API key generated: {KeyName} for client: {ClientName}", name, clientName);

        return Task.FromResult(apiKey);
    }

    public Task<bool> RevokeApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        var hashedKey = HashApiKey(apiKey);
        
        if (_apiKeys.TryGetValue(hashedKey, out var apiKeyInfo))
        {
            apiKeyInfo.IsActive = false;
            _logger.LogInformation("API key revoked: {KeyName}", apiKeyInfo.Name);
            return Task.FromResult(true);
        }

        return Task.FromResult(false);
    }

    public Task<ApiKeyInfo?> GetApiKeyInfoAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        var hashedKey = HashApiKey(apiKey);
        _apiKeys.TryGetValue(hashedKey, out var apiKeyInfo);
        return Task.FromResult(apiKeyInfo);
    }

    public Task UpdateLastUsedAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        var hashedKey = HashApiKey(apiKey);

        if (_apiKeys.TryGetValue(hashedKey, out var apiKeyInfo))
        {
            apiKeyInfo.LastUsedAt = DateTime.UtcNow;
        }

        return Task.CompletedTask;
    }

    public Task<IEnumerable<ApiKeyInfo>> GetAllApiKeysAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<ApiKeyInfo>>(_apiKeys.Values.ToList());
    }

    private void InitializeMasterApiKey()
    {
        if (!string.IsNullOrEmpty(_settings.MasterApiKey))
        {
            var hashedKey = HashApiKey(_settings.MasterApiKey);
            
            if (!_apiKeys.ContainsKey(hashedKey))
            {
                var masterKeyInfo = new ApiKeyInfo
                {
                    Id = "master",
                    Name = "Master API Key",
                    ClientName = "CI Solution",
                    IsActive = true,
                    RateLimit = int.MaxValue, // No rate limit for master key
                    RateLimitWindowMinutes = _settings.RateLimitWindowMinutes,
                    CreatedAt = DateTime.UtcNow
                };

                _apiKeys[hashedKey] = masterKeyInfo;
                _logger.LogInformation("Master API key initialized");
            }
        }
    }

    private Task<ApiKeyValidationResult> CheckRateLimitAsync(string hashedKey, ApiKeyInfo apiKeyInfo, string? clientIpAddress)
    {
        var rateLimitKey = $"{hashedKey}:{clientIpAddress}";
        var now = DateTime.UtcNow;
        var windowStart = now.AddMinutes(-apiKeyInfo.RateLimitWindowMinutes);

        var rateLimitInfo = _rateLimits.GetOrAdd(rateLimitKey, _ => new RateLimitInfo
        {
            WindowStart = now,
            RequestCount = 0
        });

        // Reset window if expired
        if (rateLimitInfo.WindowStart <= windowStart)
        {
            rateLimitInfo.WindowStart = now;
            rateLimitInfo.RequestCount = 0;
        }

        // Check if limit exceeded
        if (rateLimitInfo.RequestCount >= apiKeyInfo.RateLimit)
        {
            var resetTime = rateLimitInfo.WindowStart.AddMinutes(apiKeyInfo.RateLimitWindowMinutes);
            _logger.LogWarning("Rate limit exceeded for API key: {KeyName} from IP: {IpAddress}", apiKeyInfo.Name, clientIpAddress);
            return Task.FromResult(ApiKeyValidationResult.RateLimitExceededResult(resetTime));
        }

        // Increment request count
        Interlocked.Increment(ref rateLimitInfo.RequestCount);

        return Task.FromResult(ApiKeyValidationResult.Success(apiKeyInfo, apiKeyInfo.RateLimit - rateLimitInfo.RequestCount));
    }

    private static string GenerateSecureApiKey()
    {
        // Generate a 32-byte random key and encode as base64
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").TrimEnd('=');
    }

    private static string HashApiKey(string apiKey)
    {
        // Hash the API key for secure storage
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(apiKey));
        return Convert.ToBase64String(hashedBytes);
    }

    private class RateLimitInfo
    {
        public DateTime WindowStart { get; set; }
        public int RequestCount;
    }
}
