using GDAPI.Modules.AuthoringTool.Domain.Entities;
using GDAPI.Modules.AuthoringTool.Domain.Interfaces;
using GDAPI.Modules.AuthoringTool.Infrastructure.Data;
using Dapper;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Data.Common;

namespace GDAPI.Modules.AuthoringTool.Infrastructure.Repositories;

/// <summary>
/// Authoring repository implementation using ADO.NET with Dapper for AuthoringTool module
/// </summary>
public class AuthoringRepository : IAuthoringRepository
{
    private readonly IAuthoringToolDbConnectionFactory _connectionFactory;

    /// <summary>
    /// Initializes a new instance of the <see cref="AuthoringRepository"/> class.
    /// </summary>
    /// <param name="connectionFactory">The database connection factory.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="connectionFactory"/> is null.</exception>
    public AuthoringRepository(IAuthoringToolDbConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
    }

    /// <summary>
    /// Gets paginated authoring items using stored procedure with filtering support.
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1).</param>
    /// <param name="pageSize">Page size (default: 10).</param>
    /// <param name="searchTerm">Optional search term for authoring title or description.</param>
    /// <param name="statusFilter">Optional status filter.</param>
    /// <param name="categoryFilter">Optional category filter.</param>
    /// <param name="authorFilter">Optional author filter.</param>
    /// <param name="departmentFilter">Optional department filter.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Paginated result of authoring items with total count.</returns>
    public async Task<(IEnumerable<Authoring> Items, int TotalCount)> GetAuthoringSamplePagedAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? searchTerm = null,
        string? statusFilter = null,
        string? categoryFilter = null,
        string? authorFilter = null,
        string? departmentFilter = null,
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        // First get all data from stored procedure
        var allData = await connection.QueryAsync<Authoring>("EXEC GetAuthoring_Sample");

        // Apply filters in memory (for simplicity)
        var filteredData = allData.AsEnumerable();

        if (!string.IsNullOrEmpty(searchTerm))
        {
            filteredData = filteredData.Where(x =>
                x.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (x.Description != null && x.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
        }

        if (!string.IsNullOrEmpty(statusFilter))
        {
            filteredData = filteredData.Where(x => x.Status == statusFilter);
        }

        if (!string.IsNullOrEmpty(categoryFilter))
        {
            filteredData = filteredData.Where(x => x.Category == categoryFilter);
        }

        if (!string.IsNullOrEmpty(authorFilter))
        {
            filteredData = filteredData.Where(x => x.Author == authorFilter);
        }

        if (!string.IsNullOrEmpty(departmentFilter))
        {
            filteredData = filteredData.Where(x => x.Department == departmentFilter);
        }

        var totalCount = filteredData.Count();

        // Apply pagination
        var offset = (pageNumber - 1) * pageSize;
        var pagedData = filteredData
            .OrderBy(x => x.Title)
            .Skip(offset)
            .Take(pageSize);

        return (pagedData, totalCount);
    }

    #region : Get User Details 

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves user details.

    /// <summary>
    /// Retrieves all users from the database using the CI_BindUsersList stored procedure.
    /// </summary>
    /// <param name="type">User type filter (default: "All").</param>
    /// <returns>List of user details.</returns>
    /// <exception cref="Exception">Thrown if an error occurs during database access.</exception>
    public async Task<List<UserDetails>> GetAllUsersAsync(string type = "All")
    {
        try
        {
            using var connection = _connectionFactory.CreateConnection();
            return (await connection.QueryAsync<UserDetails>(
                "CI_BindUsersList",
                new { Type = type },
                commandType: CommandType.StoredProcedure
            )).ToList();
        }
        catch (Exception ex)
        {
            // Log the exception (use your logging framework)
            // throw or handle as needed
            throw;
        }
    }

    #endregion

    #region : Clinical Comparators

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves Clinical Comparators details.

    /// <summary>
    /// Retrieves clinical comparators grouped by indications from the database using the CI_BindClinicalComparatorsList stored procedure.
    /// </summary>
    /// <returns>
    /// An enumerable of <see cref="ClinicalComparatorsGroupByIndications"/> containing grouped clinical comparator entries.
    /// </returns>
    /// <exception cref="Exception">Thrown if an error occurs during database access.</exception>
    public async Task<IEnumerable<ClinicalComparatorsGroupByIndications>> GetClinicalComparatorsAsync()
    {
        try
        {
            using var connection = _connectionFactory.CreateConnection();
            var flatData = await connection.QueryAsync<ClinicalComparatorsData>(
                "[dbo].[CI_BindClinicalComparatorsList]",
                new { Type = "All" },
                commandType: CommandType.StoredProcedure
            );

            return flatData?.Any() != true
                ? Enumerable.Empty<ClinicalComparatorsGroupByIndications>()
                : flatData.GroupBy(dto => dto.IndicationName)
                    .Select(group => new ClinicalComparatorsGroupByIndications
                    {
                        IndicationName = group.Key,
                        Entries = group.Select(dto => new ClinicalComparatorsEntry
                        {
                            Id = dto.Id,
                            VersionName = dto.VersionName,
                            Status = dto.PublishStatus == "Published" ? ClinicalComparatorsStatus.Published : ClinicalComparatorsStatus.Draft,
                            IsDraftUpdate = dto.IsDraftUpdate,
                            AccessType = dto.AccessType,
                            IsAccessTypeFirewalled = dto.AccessType == "Firewalled",
                            OwnerInitials = dto.ShortCode,
                            OwnerName = dto.DisplayName,
                            LastModifiedDisplay = dto.LastModified,
                            ClientName = dto.ClientName,
                            IsClientFirewalled = dto.AccessType == "Firewalled"
                        }).ToList()
                    });
        }
        catch (Exception ex)
        {
            // Log the exception (use your logging framework)
            // throw or handle as needed
            throw;
        }
    }

    #endregion
}
