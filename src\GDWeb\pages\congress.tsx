import type { NextPage } from "next";
import Head from "next/head";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart3, 
  TrendingUp, 
  Shield, 
  Calendar, 
  Settings, 
  Target,
  Activity,
  Grid3X3,
  Table as TableIcon,
  Zap,
  TreePine,
  Layers,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Home as HomeIcon,
  Map,
  FlaskConical,
  Scale,
  Database,
  Lightbulb,
  Bot,
  FileText,
  Building2,
  Search,
  Users,
  MessageSquare,
  Globe,
  Download,
  Bookmark,
  Phone,
  Mail,
  Video,
  Filter,
  X,
  PieChart,
  Loader2,
  Presentation,
  Eye,
  ExternalLink
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import navigationConfig from "@/data/navigation-config.json";

const Congress: NextPage = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedSections, setExpandedSections] = useState<{[key: number]: boolean}>({
    2: true, // Clinical Trials Comparator expanded
    3: true  // Congress Intelligence expanded
  });
  const [consultantDropdownOpen, setConsultantDropdownOpen] = useState(false);
  const [diseaseDropdownOpen, setDiseaseDropdownOpen] = useState(false);

  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Congress and data selection states
  const [selectedCongress, setSelectedCongress] = useState<string>('ddw-2025');
  const [selectedDataItem, setSelectedDataItem] = useState<string>('');
  const [showChart, setShowChart] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(25);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Start loading immediately
        setLoadingProgress(5);

        // Use requestAnimationFrame to ensure UI updates before heavy operations
        await new Promise(resolve => requestAnimationFrame(resolve));

        setLoadingProgress(15);

        // Simulate loading congress data
        await new Promise(resolve => setTimeout(resolve, 50));
        setLoadingProgress(45);

        // Simulate loading presentation data
        await new Promise(resolve => setTimeout(resolve, 50));
        setLoadingProgress(85);

        setLoadingProgress(100);

        // Minimal delay to show completion
        setTimeout(() => {
          setIsLoading(false);
        }, 150);
      } catch (error) {
        console.error('Error loading data:', error);
        setIsLoading(false);
      }
    };

    // Start loading immediately on next tick
    setTimeout(() => {
      loadData();
    }, 0);
  }, []);

  const toggleSection = (index: number) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Helper function to get icon component by name
  const getIconComponent = (iconName: string) => {
    const icons = {
      HomeIcon: <HomeIcon className="w-4 h-4" />,
      Map: <Map className="w-4 h-4" />,
      Calendar: <Calendar className="w-4 h-4" />,
      Target: <Target className="w-4 h-4" />,
      BarChart3: <BarChart3 className="w-4 h-4" />,
      FlaskConical: <FlaskConical className="w-4 h-4" />,
      Scale: <Scale className="w-4 h-4" />,
      Users: <Users className="w-4 h-4" />,
      MessageSquare: <MessageSquare className="w-4 h-4" />,
      Settings: <Settings className="w-4 h-4" />,
      Database: <Database className="w-4 h-4" />,
      Activity: <Activity className="w-4 h-4" />,
      Lightbulb: <Lightbulb className="w-4 h-4" />,
      Grid3X3: <Grid3X3 className="w-4 h-4" />,
      Search: <Search className="w-4 h-4" />,
      TrendingUp: <TrendingUp className="w-4 h-4" />,
      Building2: <Building2 className="w-4 h-4" />,
      Globe: <Globe className="w-4 h-4" />,
      Bot: <Bot className="w-4 h-4" />,
      FileText: <FileText className="w-4 h-4" />
    };
    return icons[iconName as keyof typeof icons] || <HomeIcon className="w-4 h-4" />;
  };

  const navigationItems = navigationConfig.navigationItems.map(item => ({
    ...item,
    icon: getIconComponent(item.icon),
    children: item.children?.map(child => ({
      ...child,
      icon: getIconComponent(child.icon)
    }))
  }));

  // Congress data configuration
  const congressData = {
    'ddw-2025': {
      name: 'DDW 2025',
      location: 'Washington, DC',
      date: 'May 2025',
      status: 'Past',
      sampleData: [
        {
          id: 'ddw-2025-sample-1',
          results_publication: 'Vedolizumab Long-term Safety and Efficacy in Crohn\'s Disease: 5-Year Follow-up Data from GEMINI 2',
          results_link: 'https://www.ddw.org/abstracts/vedolizumab-long-term-safety-efficacy-cd-gemini2-5year'
        },
        {
          id: 'ddw-2025-sample-2',
          results_publication: 'Real-world Effectiveness of Risankizumab in Moderate-to-Severe Crohn\'s Disease: Multi-center Registry Analysis',
          results_link: 'https://www.ddw.org/abstracts/risankizumab-real-world-effectiveness-cd-registry'
        },
        {
          id: 'ddw-2025-sample-3',
          results_publication: 'Comparative Safety Profile of Advanced Therapies in IBD: Systematic Review and Network Meta-analysis',
          results_link: 'https://www.ddw.org/abstracts/comparative-safety-advanced-therapies-ibd-network-meta-analysis'
        },
        {
          id: 'ddw-2025-sample-4',
          results_publication: 'Biomarker-guided Treatment Selection in Ulcerative Colitis: Results from the PRECISION-UC Study',
          results_link: 'https://www.ddw.org/abstracts/biomarker-guided-treatment-uc-precision-study'
        },
        {
          id: 'ddw-2025-sample-5',
          results_publication: 'Novel Oral JAK Inhibitor TYK2-JH2 in Crohn\'s Disease: Phase 2 Dose-Finding Study Results',
          results_link: 'https://www.ddw.org/abstracts/novel-oral-jak-inhibitor-tyk2-cd-phase2-dose-finding'
        }
      ],
      dataGroups: {
        'Vedolizumab - GEMINI 2': [
          {
            id: 'gemini-2-efficacy-primary',
            title: 'Primary Efficacy Endpoints',
            description: 'Clinical remission and response at Week 52',
            chartType: 'Column Chart',
            icon: <BarChart3 className="w-5 h-5 text-blue-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          },
          {
            id: 'gemini-2-efficacy-secondary',
            title: 'Secondary Efficacy Endpoints',
            description: 'Endoscopic remission and mucosal healing',
            chartType: 'Column Chart',
            icon: <BarChart3 className="w-5 h-5 text-blue-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          },
          {
            id: 'gemini-2-safety',
            title: 'Long-term Safety Profile',
            description: 'Adverse events and serious infections over 52 weeks',
            chartType: 'Safety Matrix',
            icon: <Shield className="w-5 h-5 text-red-600" />,
            category: 'Safety',
            categoryColor: 'bg-red-100 text-red-800 border-red-200'
          }
        ],
        'Risankizumab - ADVANCE': [
          {
            id: 'advance-efficacy',
            title: 'Primary Efficacy Results',
            description: 'Clinical remission at Week 12 induction',
            chartType: 'Hazard Plot',
            icon: <Activity className="w-5 h-5 text-green-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          }
        ]
      }
    },
    'ecco-2025': {
      name: 'ECCO 2025',
      location: 'Amsterdam, Netherlands',
      date: 'February 2025',
      status: 'Past',
      sampleData: [
        {
          id: 'ecco-2025-sample-1',
          results_publication: 'European Multi-center Study on Fecal Microbiota Transplantation in Refractory IBD: 2-Year Outcomes',
          results_link: 'https://www.ecco-ibd.eu/congress/abstracts/fmt-refractory-ibd-2year-outcomes'
        },
        {
          id: 'ecco-2025-sample-2',
          results_publication: 'Risankizumab Maintenance Therapy in UC: Long-term Safety and Efficacy from UNITY Extension Study',
          results_link: 'https://www.ecco-ibd.eu/congress/abstracts/risankizumab-maintenance-uc-unity-extension'
        },
        {
          id: 'ecco-2025-sample-3',
          results_publication: 'Personalized Treatment Algorithms in Pediatric IBD: Machine Learning Approach to Optimize Outcomes',
          results_link: 'https://www.ecco-ibd.eu/congress/abstracts/personalized-treatment-pediatric-ibd-ml-approach'
        },
        {
          id: 'ecco-2025-sample-4',
          results_publication: 'Novel Endoscopic Scoring System for Crohn\'s Disease: Validation in European Cohort',
          results_link: 'https://www.ecco-ibd.eu/congress/abstracts/novel-endoscopic-scoring-cd-european-validation'
        }
      ],
      dataGroups: {
        'Risankizumab - UNITY-UC': [
          {
            id: 'unity-uc-efficacy',
            title: 'Maintenance Efficacy Results',
            description: 'Clinical remission maintenance at Week 52',
            chartType: 'Hazard Plot',
            icon: <Activity className="w-5 h-5 text-green-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          },
          {
            id: 'unity-uc-safety',
            title: 'Long-term Safety',
            description: 'Safety profile in maintenance phase',
            chartType: 'Safety Matrix',
            icon: <Shield className="w-5 h-5 text-red-600" />,
            category: 'Safety',
            categoryColor: 'bg-red-100 text-red-800 border-red-200'
          }
        ]
      }
    },
    'uegw-2024': {
      name: 'UEGW 2024',
      location: 'Vienna, Austria',
      date: 'October 2024',
      status: 'Past',
      sampleData: [
        {
          id: 'uegw-2024-sample-1',
          results_publication: 'Ozanimod in Ulcerative Colitis: Cardiac Safety Monitoring Results from PROMETHEUS Trial',
          results_link: 'https://www.ueg.eu/abstracts/ozanimod-uc-cardiac-safety-prometheus-trial'
        },
        {
          id: 'uegw-2024-sample-2',
          results_publication: 'AI-powered Histopathology Analysis for IBD Diagnosis: Multi-center Validation Study',
          results_link: 'https://www.ueg.eu/abstracts/ai-histopathology-ibd-diagnosis-validation'
        },
        {
          id: 'uegw-2024-sample-3',
          results_publication: 'Comparative Effectiveness of Biosimilar vs Originator Infliximab: Real-world European Database Analysis',
          results_link: 'https://www.ueg.eu/abstracts/biosimilar-originator-infliximab-effectiveness-european-analysis'
        },
        {
          id: 'uegw-2024-sample-4',
          results_publication: 'Novel Dietary Intervention in Active Crohn\'s Disease: Randomized Controlled Trial Results',
          results_link: 'https://www.ueg.eu/abstracts/dietary-intervention-active-cd-rct-results'
        }
      ],
      dataGroups: {
        'Ozanimod - PROMETHEUS': [
          {
            id: 'prometheus-efficacy',
            title: 'Primary Efficacy Data',
            description: 'Clinical remission at Week 12',
            chartType: 'Column Chart',
            icon: <BarChart3 className="w-5 h-5 text-blue-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          },
          {
            id: 'prometheus-safety',
            title: 'Safety Profile',
            description: 'Cardiac monitoring and safety outcomes',
            chartType: 'Safety Matrix',
            icon: <Shield className="w-5 h-5 text-red-600" />,
            category: 'Safety',
            categoryColor: 'bg-red-100 text-red-800 border-red-200'
          }
        ]
      }
    },
    'ddw-2024': {
      name: 'DDW 2024',
      location: 'Chicago, IL',
      date: 'May 2024',
      status: 'Past',
      sampleData: [
        {
          id: 'ddw-2024-sample-1',
          results_publication: 'Mirikizumab Induction and Maintenance in Crohn\'s Disease: Integrated Analysis of STELLAR Program',
          results_link: 'https://www.ddw.org/abstracts/mirikizumab-induction-maintenance-cd-stellar-integrated'
        },
        {
          id: 'ddw-2024-sample-2',
          results_publication: 'Long-term Outcomes of Fecal Calprotectin-guided Treatment in IBD: 3-Year Prospective Cohort',
          results_link: 'https://www.ddw.org/abstracts/fecal-calprotectin-guided-treatment-ibd-3year-cohort'
        },
        {
          id: 'ddw-2024-sample-3',
          results_publication: 'Combination Therapy with Anti-TNF and Methotrexate in Crohn\'s Disease: Systematic Review and Meta-analysis',
          results_link: 'https://www.ddw.org/abstracts/combination-anti-tnf-methotrexate-cd-meta-analysis'
        },
        {
          id: 'ddw-2024-sample-4',
          results_publication: 'Telehealth Monitoring in IBD During COVID-19: Impact on Disease Management and Patient Outcomes',
          results_link: 'https://www.ddw.org/abstracts/telehealth-monitoring-ibd-covid19-disease-management'
        }
      ],
      dataGroups: {
        'Mirikizumab - STELLAR-CD': [
          {
            id: 'stellar-cd-induction',
            title: 'Induction Study Results',
            description: 'Week 12 primary efficacy endpoints',
            chartType: 'Column Chart',
            icon: <BarChart3 className="w-5 h-5 text-blue-600" />,
            category: 'Efficacy',
            categoryColor: 'bg-blue-100 text-blue-800 border-blue-200'
          },
          {
            id: 'stellar-cd-safety',
            title: 'Integrated Safety Analysis',
            description: 'Pooled safety across induction and maintenance',
            chartType: 'Safety Matrix',
            icon: <Shield className="w-5 h-5 text-red-600" />,
            category: 'Safety',
            categoryColor: 'bg-red-100 text-red-800 border-red-200'
          }
        ]
      }
    }
  };

  // Define the DataItem interface
  interface DataItem {
    id: string;
    title: string;
    description: string;
    chartType: string;
    icon: React.ReactNode;
    category: string;
    categoryColor: string;
  }

  const currentCongress = congressData[selectedCongress as keyof typeof congressData];
  const getAllDataItems = (congress: any): DataItem[] => {
    if (!congress?.dataGroups) return [];
    return Object.values(congress.dataGroups).flat() as DataItem[];
  };
  const selectedData = getAllDataItems(currentCongress).find((item: DataItem) => item.id === selectedDataItem);

  // Function to get 3 random sample sources from selected congress
  const getRandomSampleSources = () => {
    if (!currentCongress?.sampleData) return [];
    const shuffled = [...currentCongress.sampleData].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3);
  };

  // Load efficacy data for congress filtering
  const [efficacyData, setEfficacyData] = useState<any[]>([]);

  // Load efficacy data on component mount
  useEffect(() => {
    const loadEfficacyData = async () => {
      try {
        const efficacyModule = await import('@/data/comparator/efficacy-data');
        setEfficacyData(efficacyModule.efficacyData);
      } catch (error) {
        console.error('Error loading efficacy data:', error);
      }
    };

    loadEfficacyData();
  }, []);

  // Generate table data based on selected congress and data item
  const generateTableData = () => {
    if (!selectedDataItem || efficacyData.length === 0) return [];

    // Filter efficacy data for QUASAR trial specifically for congress data
    return efficacyData.filter(item => 
      item.trial_name === 'QUASAR' && 
      item.asset && 
      item.endpoint &&
      item.timepoint
    ).map(item => ({
      ...item,
      results_link: item.result_link || item.results_link || '-'
    }));
  };

  const tableData = generateTableData();

  const handleDataItemSelect = (itemId: string) => {
    setSelectedDataItem(itemId);
    setShowChart(true);
  };

  const dataSelectionOptions = getAllDataItems(currentCongress);

  // Get all columns from the current dataset in specified order
  const getTableColumns = () => {
    if (tableData.length === 0) return [];

    // Define column order
    const primaryCols = [
      'asset', 'trial_name', 'trial_phase', 'indication', 'sub_group', 
      'portion_of_study', 'dose', 'dosing_regimen', 'roa'
    ];

    // Add endpoint specific columns
    const endpointCols = ['umbrella_endpoint', 'endpoint', 'data_handling'];

    // Get all available columns
    const allColumns = Object.keys(tableData[0]);

    // Get remaining columns (excluding primary and endpoint cols)
    const remainingCols = allColumns.filter(col => 
      !primaryCols.includes(col) && 
      !endpointCols.includes(col) && 
      col !== 'results_link'
    );

    // Return ordered columns with Results at the end
    return [...primaryCols, ...endpointCols, ...remainingCols, 'results_link'];
  };

  // Export functionality
  const exportToCSV = () => {
    if (tableData.length === 0) return;

    const headers = getTableColumns().map(col => 
      col === 'results_link' ? 'Results' : 
      col.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    );

    const csvContent = [
      headers.join(','),
      ...tableData.map(row => 
        getTableColumns().map(col => {
          const value = row[col];
          if (col === 'results_link') {
            return value && value !== '-' && value !== 'N/A' ? value : '';
          }
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value || '';
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `congress-data-${selectedCongress}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = () => {
    alert('Excel export functionality would be implemented with a library like xlsx. For now, please use CSV export.');
  };

  // Calculate pagination
  const totalPages = recordsPerPage === -1 ? 1 : Math.ceil(tableData.length / recordsPerPage);
  const startIndex = recordsPerPage === -1 ? 0 : (currentPage - 1) * recordsPerPage;
  const endIndex = recordsPerPage === -1 ? tableData.length : startIndex + recordsPerPage;
  const paginatedData = tableData.slice(startIndex, endIndex);

  // Handle page navigation
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // Handle pagination change
  const handlePaginationChange = (newRecordsPerPage: number) => {
    setRecordsPerPage(newRecordsPerPage);
    setCurrentPage(1);
  };

  // Reset to first page when data changes
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedDataItem, selectedCongress]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Congress Intelligence | IBD CI Solution</title>
          <meta name="description" content="Review and analyze congress presentation data and scientific meeting intelligence" />
        </Head>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 text-center space-y-6 max-w-md mx-4">
              <div className="relative">
                <Loader2 className="w-16 h-16 text-blue-600 animate-spin mx-auto" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
              </div>
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-gray-900">Loading Congress Data</h2>
                <p className="text-gray-600">Preparing scientific presentation data...</p>
                <div className="w-80 mx-auto">
                  <Progress value={loadingProgress} className="h-2" />
                </div>
                <p className="text-sm text-gray-500">{loadingProgress}% Complete</p>
              </div>
            </div>
          </div>
        )}

        <div className="flex">
          {/* Sidebar */}
          <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 border-r bg-white border-gray-200 flex flex-col`}>
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                {sidebarOpen ? (
                  <div className="flex items-center space-x-3">
                    <img 
                      src="/gd-logo.png" 
                      alt="GlobalData" 
                      className="h-8 w-auto"
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-8">
                    <img 
                      src="/gd-icon.png" 
                      alt="GlobalData" 
                      className="h-8 w-8"
                    />
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="text-gray-600 hover:bg-gray-100"
                >
                  {sidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              <nav className="space-y-2">
                {navigationItems.map((item, index) => (
                  <div key={index}>
                    {!sidebarOpen ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div 
                            className={`flex items-center justify-center px-3 py-2 rounded-md cursor-pointer transition-colors ${
                              item.label === 'Congress Intelligence'
                                ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                                : 'text-gray-700 hover:bg-gray-100'
                            }`}
                            onClick={() => item.children && sidebarOpen && toggleSection(index)}
                          >
                            {item.icon}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="ml-2">
                          <p>{item.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <div 
                        className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                          item.label === 'Clinical Trials Comparator'
                            ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => {
                          if (item.label === 'Home') {
                            window.location.href = '/';
                          } else if ('href' in item && item.href && typeof item.href === 'string') {
                            if (item.href !== '/congress') {
                              window.location.href = item.href;
                            }
                          } else if (item.children && sidebarOpen) {
                            toggleSection(index);
                          }
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          {item.icon}
                          {sidebarOpen && <span className="text-xs font-medium">{item.label}</span>}
                        </div>
                        {item.children && sidebarOpen && (
                          expandedSections[index] ? 
                            <ChevronUp className="w-4 h-4" /> : 
                            <ChevronDown className="w-4 h-4" />
                        )}
                      </div>
                    )}
                    {item.children && sidebarOpen && expandedSections[index] && (
                      <div className="ml-6 mt-2 space-y-1">
                        {item.children.map((child, childIndex) => (
                          <div 
                            key={childIndex} 
                            className={`flex items-center space-x-3 px-3 py-1 rounded-md cursor-pointer hover:bg-gray-50 ${
                              child.label === 'Trial Results Comparator'
                                ? 'text-blue-700 bg-blue-50 font-medium' 
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                            onClick={() => {
                              if (child.href) {
                                window.location.href = child.href;
                              }
                            }}
                          >
                            {child.icon}
                            <span className="text-xs">{child.label}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>

              {/* Document Library Section */}
              {sidebarOpen && navigationConfig.documentLibrary && (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mx-2">
                    <div className="mb-2">
                      <h3 className="text-xs font-semibold text-blue-800 uppercase tracking-wide">{navigationConfig.documentLibrary.title}</h3>
                    </div>
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-blue-700 hover:text-blue-900 hover:bg-blue-100 transition-colors">
                      {navigationConfig.documentLibrary.link?.icon && getIconComponent(navigationConfig.documentLibrary.link.icon)}
                      <span className="text-xs font-medium">{navigationConfig.documentLibrary.link?.label}</span>
                    </div>
                  </div>

                  {/* Feedback Section */}
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors mx-2">
                      <MessageSquare className="w-4 h-4" />
                      <span className="text-xs font-medium">Help us improve this solution</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 px-8 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.href = '/comparator'}
                        className="text-gray-600 hover:text-gray-900 p-1"
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Back to Comparator
                      </Button>
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900">Trial Results Comparator</h1>
                    <p className="text-sm text-gray-600 mt-1">Congress Intelligence - Review latest congress data and scientific presentation insights</p>
                  </div>
                  <div className="relative">
                    <div 
                      className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full cursor-pointer hover:bg-blue-200 transition-colors"
                      onClick={() => setDiseaseDropdownOpen(!diseaseDropdownOpen)}
                    >
                      <span className="text-sm font-medium text-blue-800">IBD</span>
                      <ChevronDown className={`w-3 h-3 text-blue-600 transition-transform ${diseaseDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </div>
                </div>

                {/* Solution Consultant */}
                <div className="relative">
                  <div 
                    className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors border border-gray-200 bg-white shadow-sm"
                    onClick={() => {
                      setConsultantDropdownOpen(!consultantDropdownOpen);
                      setDiseaseDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      {/* Fake headshot */}
                      <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                        <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500 font-medium">Your Solution Consultant</div>
                        <div className="text-sm font-semibold text-gray-900">James Davidson</div>
                      </div>
                      <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${consultantDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-8 py-8 space-y-8">
              {/* Congress Selection and Data Display */}
              <div className="grid grid-cols-12 gap-8">
                {/* Left Side - Congress Vertical Tabs */}
                <div className="col-span-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center text-base">
                        <Calendar className="w-4 h-4 mr-2" />
                        Scientific Congresses
                      </CardTitle>
                      <CardDescription className="text-sm">
                        Select a congress to view available data
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="space-y-1">
                        {Object.entries(congressData).map(([key, congress]) => (
                          <div
                            key={key}
                            className={`px-3 py-2 cursor-pointer border-l-4 transition-all hover:bg-gray-50 ${
                              selectedCongress === key
                                ? 'border-blue-500 bg-blue-50 text-blue-900'
                                : 'border-transparent hover:border-gray-200'
                            }`}
                            onClick={() => {
                              setSelectedCongress(key);
                              setSelectedDataItem('');
                              setShowChart(false);
                            }}
                          >
                            <div>
                              <div className="font-semibold text-sm">{congress.name}</div>
                              <div className="text-xs text-gray-500">{congress.date}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Right Side - Data Selection */}
                <div className="col-span-9">
                  {currentCongress ? (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center text-base">
                          <Presentation className="w-4 h-4 mr-2" />
                          {currentCongress.name} - Available Data
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {currentCongress.location} • {currentCongress.date}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          {Object.entries(currentCongress.dataGroups).map(([drugTrial, items]) => (
                            <div key={drugTrial} className="space-y-2">
                              <h3 className="font-semibold text-gray-900 text-sm border-b border-gray-200 pb-1">
                                {drugTrial}
                              </h3>
                              <div className="space-y-2">
                                {(items as any[]).map((item) => (
                                  <div
                                    key={item.id}
                                    className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                                      selectedDataItem === item.id
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                    onClick={() => handleDataItemSelect(item.id)}
                                  >
                                    <div className="flex items-center space-x-3">
                                      <div className="flex-shrink-0">
                                        {item.icon}
                                      </div>
                                      <div className="flex-1 min-w-0">
                                          <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                              <h4 className="text-sm font-semibold text-gray-900">{item.title}</h4>
                                              <span className="text-gray-600 text-sm">- {item.description}</span>
                                            </div>
                                            <span className={`px-2 py-0.5 border rounded-full text-xs font-semibold ${item.categoryColor}`}>
                                              {item.category}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="flex items-center justify-center h-32">
                        <div className="text-center text-gray-500">
                          <Presentation className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm">Select a congress to view available data</p>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>

              {/* Chart Section */}
              {showChart && selectedData && (
                <Card data-chart-section>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      {selectedData.icon}
                      <span className="ml-2">{selectedData.title}</span>
                    </CardTitle>
                    <CardDescription>
                      {selectedData.description} - {currentCongress.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* Chart Actions */}
                    <div className="flex justify-end mb-4 space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex items-center space-x-2"
                        onClick={() => {
                          const name = prompt('Enter a name for this saved analysis:');
                          if (name) {
                            alert(`Analysis "${name}" saved successfully!`);
                          }
                        }}
                      >
                        <Bookmark className="w-4 h-4" />
                        <span>Save</span>
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm" className="flex items-center space-x-2">
                            <Download className="w-4 h-4" />
                            <span>Export</span>
                            <ChevronDown className="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Export Chart</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => alert('Exporting as PNG...')}>
                            Export as PNG
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => alert('Exporting as PDF...')}>
                            Export as PDF
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => alert('Downloading data...')}>
                            Download Data
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Placeholder Chart */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-12 text-center">
                      <div className="space-y-4">
                        <div className="mx-auto w-16 h-16 bg-blue-200 rounded-lg flex items-center justify-center">
                          {selectedData.icon}
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {selectedData.chartType} Placeholder
                        </h3>
                        <p className="text-gray-600 max-w-md mx-auto">
                          This is where the {selectedData.chartType.toLowerCase()} for {selectedData.title} would be displayed. 
                          Chart implementation will be added based on the specific data visualization requirements.
                        </p>
                        <div className="flex items-center justify-center space-x-4 mt-6">
                          <div className="w-24 h-6 bg-blue-300 rounded animate-pulse"></div>
                          <div className="w-32 h-6 bg-blue-300 rounded animate-pulse"></div>
                          <div className="w-20 h-6 bg-blue-300 rounded animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* Sources Section for Congress Charts */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Data Sources</h4>
                      <div className="text-xs text-gray-600">
                        <a 
                          href="https://acg2024.eventscribe.net/fsPopup.asp?PresentationID=1498111&mode=presInfo" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          ACG 2024
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://ueg.eu/library/two-year-efficacy-and-safety-of-MIR-following-104-weeks-of-continuous-treatment-interim-results-from-the-lucent-3-open-label-extension-study/85233adc-743b-11ee-921f-0242ac140004" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          UEGW 2023
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://www.thelancet.com/journals/lancet/article/PIIS0140-6736(21)00666-8/fulltext" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          The Lancet 2021
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Data Table Section */}
              {showChart && selectedData && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center">
                          <TableIcon className="w-5 h-5 mr-2" />
                          Congress Data Table
                        </CardTitle>
                        <CardDescription>
                          Detailed data for {selectedData.title} ({tableData.length} records)
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center space-x-2">
                              <Settings className="w-4 h-4" />
                              <span>Configure Columns</span>
                              <ChevronDown className="w-3 h-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-64">
                            <DropdownMenuLabel>Configure Columns</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {getTableColumns().map((column) => (
                              <DropdownMenuItem key={column}>
                                <div className="flex items-center justify-between w-full">
                                  <span>{column === 'results_link' ? 'Results' : 
                                         column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                                  <span className="ml-auto">✓</span>
                                </div>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center space-x-2">
                              <Download className="w-4 h-4" />
                              <span>Export</span>
                              <ChevronDown className="w-3 h-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Export Data</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={exportToCSV}>
                              <FileText className="w-4 h-4 mr-2" />
                              Export as CSV
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={exportToExcel}>
                              <Grid3X3 className="w-4 h-4 mr-2" />
                              Export as Excel
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <div className="flex items-center space-x-2">
                                <span>Visible Columns Only</span>
                                <span className="ml-auto">✓</span>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              All Columns
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {/* Top Pagination and Scroll Controls */}
                    {tableData.length > 0 && (
                      <div className="space-y-4 pb-4 border-b border-gray-200 mb-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="text-sm text-gray-600">
                              Showing {recordsPerPage === -1 ? 'all' : `${startIndex + 1} to ${Math.min(endIndex, tableData.length)}`} of {tableData.length} records
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                  <span>Show: {recordsPerPage === -1 ? 'All' : recordsPerPage}</span>
                                  <ChevronDown className="w-3 h-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="start">
                                <DropdownMenuLabel>Records per page</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handlePaginationChange(25)}>
                                  <div className="flex items-center space-x-2">
                                    <span>25</span>
                                    {recordsPerPage === 25 && <span className="ml-auto">✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handlePaginationChange(50)}>
                                  <div className="flex items-center space-x-2">
                                    <span>50</span>
                                    {recordsPerPage === 50 && <span className="ml-auto">✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handlePaginationChange(-1)}>
                                  <div className="flex items-center space-x-2">
                                    <span>Show All</span>
                                    {recordsPerPage === -1 && <span className="ml-auto">✓</span>}
                                  </div>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>

                          {recordsPerPage !== -1 && (
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => goToPage(currentPage - 1)}
                                disabled={currentPage === 1}
                              >
                                <ChevronLeft className="w-4 h-4 mr-1" />
                                Previous
                              </Button>

                              <div className="flex items-center space-x-1">
                                {/* Show first page */}
                                {currentPage > 3 && (
                                  <>
                                    <Button
                                      variant={currentPage === 1 ? "default" : "outline"}
                                      size="sm"
                                      onClick={() => goToPage(1)}
                                      className="w-8 h-8 p-0"
                                    >
                                      1
                                    </Button>
                                    {currentPage > 4 && <span className="text-gray-400">...</span>}
                                  </>
                                )}

                                {/* Show pages around current page */}
                                {Array.from({ length: totalPages }, (_, i) => i + 1)
                                  .filter(page => page >= currentPage - 2 && page <= currentPage + 2)
                                  .map(page => (
                                    <Button
                                      key={page}
                                      variant={currentPage === page ? "default" : "outline"}
                                      size="sm"
                                      onClick={() => goToPage(page)}
                                      className="w-8 h-8 p-0"
                                    >
                                      {page}
                                    </Button>
                                  ))}

                                {/* Show last page */}
                                {currentPage < totalPages - 2 && (
                                  <>
                                    {currentPage < totalPages - 3 && <span className="text-gray-400">...</span>}
                                    <Button
                                      variant={currentPage === totalPages ? "default" : "outline"}
                                      size="sm"
                                      onClick={() => goToPage(totalPages)}
                                      className="w-8 h-8 p-0"
                                    >
                                      {totalPages}
                                    </Button>
                                  </>
                                )}
                              </div>

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => goToPage(currentPage + 1)}
                                disabled={currentPage === totalPages}
                              >
                                Next
                                <ChevronRight className="w-4 h-4 ml-1" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            {getTableColumns().map((column) => (
                              <TableHead key={column} className="whitespace-nowrap">
                                <div className="flex items-center space-x-2">
                                  <span>
                                    {column === 'results_link' ? 'Results' : 
                                     column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </span>
                                  <Filter className="w-3 h-3 text-gray-400 hover:text-gray-600 cursor-pointer" />
                                </div>
                              </TableHead>
                            ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedData.length > 0 ? (
                            paginatedData.map((row, index) => (
                              <TableRow key={startIndex + index}>
                                {getTableColumns().map((column) => (
                                  <TableCell key={column} className="whitespace-nowrap">
                                    {column === 'results_link' ? (
                                      row[column] && row[column] !== '-' && row[column] !== 'N/A' ? (
                                        <a 
                                          href={row[column]} 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 underline font-medium"
                                        >
                                          Results
                                        </a>
                                      ) : (
                                        <span className="text-gray-400">-</span>
                                      )
                                    ) : (
                                      typeof row[column] === 'number' ? 
                                        row[column].toLocaleString() : 
                                        row[column] || '-'
                                    )}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={getTableColumns().length} className="text-center py-8 text-gray-500">
                                Select a data item to view detailed results
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Bottom Pagination Controls */}
                    {tableData.length > 0 && recordsPerPage !== -1 && (
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200 mt-4">
                        <div className="text-sm text-gray-600">
                          Showing {startIndex + 1} to {Math.min(endIndex, tableData.length)} of {tableData.length} records
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(currentPage - 1)}
                            disabled={currentPage === 1}
                          >
                            <ChevronLeft className="w-4 h-4 mr-1" />
                            Previous
                          </Button>

                          <div className="flex items-center space-x-1">
                            {/* Show first page */}
                            {currentPage > 3 && (
                              <>
                                <Button
                                  variant={currentPage === 1 ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => goToPage(1)}
                                  className="w-8 h-8 p-0"
                                >
                                  1
                                </Button>
                                {currentPage > 4 && <span className="text-gray-400">...</span>}
                              </>
                            )}

                            {/* Show pages around current page */}
                            {Array.from({ length: totalPages }, (_, i) => i + 1)
                              .filter(page => page >= currentPage - 2 && page <= currentPage + 2)
                              .map(page => (
                                <Button
                                  key={page}
                                  variant={currentPage === page ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => goToPage(page)}
                                  className="w-8 h-8 p-0"
                                >
                                  {page}
                                </Button>
                              ))}

                            {/* Show last page */}
                            {currentPage < totalPages - 2 && (
                              <>
                                {currentPage < totalPages - 3 && <span className="text-gray-400">...</span>}
                                <Button
                                  variant={currentPage === totalPages ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => goToPage(totalPages)}
                                  className="w-8 h-8 p-0"
                                >
                                  {totalPages}
                                </Button>
                              </>
                            )}
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => goToPage(currentPage + 1)}
                            disabled={currentPage === totalPages}
                          >
                            Next
                            <ChevronRight className="w-4 h-4 ml-1" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Congress;