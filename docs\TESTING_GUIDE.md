# GD Solution Testing Guide

## Quick Start

### Prerequisites
- .NET 9.0 SDK
- Visual Studio 2022 or VS Code
- SQL Server LocalDB (for integration tests)

### Running Tests

#### 1. **Run All Tests**
```bash
# From solution root
dotnet test

# With detailed output
dotnet test --verbosity normal

# With code coverage
dotnet test --collect:"XPlat Code Coverage"
```

#### 2. **Run Specific Test Projects**
```bash
# Unit tests only
dotnet test tests/GDAPI.UnitTests/

# Integration tests only
dotnet test tests/GDAPI.IntegrationTests/
```

#### 3. **Run Tests by Category**
```bash
# Run tests with specific filter
dotnet test --filter "Category=Unit"
dotnet test --filter "Category=Integration"

# Run tests by namespace
dotnet test --filter "FullyQualifiedName~UserManagement"
```

## Test Project Overview

### Unit Tests (`GDAPI.UnitTests`)

#### **Shared Components Tests**
- **Exception Tests**: Custom exception handling and validation
- **Middleware Tests**: Global exception handling and API key authentication
- **Authentication Tests**: API key validation and rate limiting

#### **Module Tests**
- **UserManagement**: User entities, repositories, and services
- **ProductManagement**: Investigator entities, repositories, and services  
- **AuthoringTool**: Authoring entities, repositories, and services

### Integration Tests (`GDAPI.IntegrationTests`)

#### **API Controller Tests**
- **InvestigatorController**: Full API endpoint testing with authentication
- **End-to-end scenarios**: Request/response validation, error handling

## Test Execution Examples

### 1. **Running Unit Tests**

```bash
# Run all unit tests
dotnet test tests/GDAPI.UnitTests/

# Expected output:
# Starting test execution, please wait...
# A total of 1 test files matched the specified pattern.
# 
# Passed!  - Failed:     0, Passed:    45, Skipped:     0, Total:    45
```

### 2. **Running Integration Tests**

```bash
# Run integration tests (requires database)
dotnet test tests/GDAPI.IntegrationTests/

# Expected output:
# Starting test execution, please wait...
# A total of 1 test files matched the specified pattern.
# 
# Passed!  - Failed:     0, Passed:    12, Skipped:     0, Total:    12
```

### 3. **Running with Code Coverage**

```bash
# Generate coverage report
dotnet test --collect:"XPlat Code Coverage" --results-directory ./TestResults

# Install report generator (one-time)
dotnet tool install -g dotnet-reportgenerator-globaltool

# Generate HTML report
reportgenerator -reports:"./TestResults/**/coverage.cobertura.xml" -targetdir:"./TestResults/CoverageReport" -reporttypes:Html

# Open coverage report
start ./TestResults/CoverageReport/index.html
```

## Visual Studio Testing

### 1. **Test Explorer**
- Open **Test Explorer** (Test → Test Explorer)
- Build solution to discover tests
- Run tests individually or in groups
- View test results and output

### 2. **Running Tests**
- **Run All**: Ctrl+R, A
- **Run Selected**: Ctrl+R, T
- **Debug Test**: Ctrl+R, Ctrl+T

### 3. **Code Coverage**
- **Analyze Code Coverage**: Test → Analyze Code Coverage for All Tests
- View coverage results in Code Coverage Results window
- Navigate to uncovered code

## Test Configuration

### 1. **Unit Test Configuration**

The unit tests use in-memory mocking and don't require external dependencies:

```csharp
// Example test setup
public class UserServiceTests
{
    private readonly Mock<IUserRepository> _repositoryMock;
    private readonly Mock<ILogger<UserService>> _loggerMock;
    private readonly UserService _service;

    public UserServiceTests()
    {
        _repositoryMock = new Mock<IUserRepository>();
        _loggerMock = new Mock<ILogger<UserService>>();
        _service = new UserService(_repositoryMock.Object, _loggerMock.Object);
    }
}
```

### 2. **Integration Test Configuration**

Integration tests use `appsettings.Test.json` for configuration:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=GDSolutionDB_Test;Trusted_Connection=true"
  },
  "ApiKeySettings": {
    "TestApiKey": "test-api-key-for-integration-tests"
  }
}
```

## Troubleshooting

### 1. **Common Issues**

#### **Tests Not Discovered**
```bash
# Clean and rebuild
dotnet clean
dotnet build
dotnet test
```

#### **Database Connection Issues**
```bash
# Verify LocalDB is running
sqllocaldb info mssqllocaldb

# Start LocalDB if needed
sqllocaldb start mssqllocaldb
```

#### **Package Restore Issues**
```bash
# Restore packages
dotnet restore

# Clear package cache if needed
dotnet nuget locals all --clear
dotnet restore
```

### 2. **Test Debugging**

#### **Debug Failing Tests**
1. Set breakpoints in test code
2. Right-click test in Test Explorer
3. Select "Debug Selected Tests"
4. Step through code to identify issues

#### **View Test Output**
1. Click on failed test in Test Explorer
2. View output in Test Detail Summary
3. Check stack trace for error details

### 3. **Performance Issues**

#### **Slow Test Execution**
- Run tests in parallel: `dotnet test --parallel`
- Use test filters to run specific tests
- Check for database connection timeouts
- Review test setup/teardown performance

## Continuous Integration

### 1. **GitHub Actions Example**

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal --collect:"XPlat Code Coverage"
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
```

### 2. **Azure DevOps Example**

```yaml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: DotNetCoreCLI@2
  displayName: 'Restore packages'
  inputs:
    command: 'restore'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Build solution'
  inputs:
    command: 'build'
    projects: '**/*.csproj'
    arguments: '--no-restore'

- task: DotNetCoreCLI@2
  displayName: 'Run tests'
  inputs:
    command: 'test'
    projects: '**/*Tests.csproj'
    arguments: '--no-build --collect:"XPlat Code Coverage"'
```

## Test Maintenance

### 1. **Regular Tasks**
- Review and update test data
- Remove obsolete tests
- Add tests for new features
- Monitor test execution time

### 2. **Code Coverage Goals**
- Maintain minimum 80% coverage
- Focus on business logic coverage
- Review coverage reports regularly
- Add tests for uncovered code

### 3. **Test Quality**
- Keep tests simple and focused
- Use descriptive test names
- Maintain test independence
- Regular refactoring of test code

## Best Practices Summary

1. **Write tests first** (TDD approach when possible)
2. **Keep tests fast** and independent
3. **Use meaningful test names** that describe the scenario
4. **Follow AAA pattern** (Arrange, Act, Assert)
5. **Mock external dependencies** in unit tests
6. **Test edge cases** and error conditions
7. **Maintain high code coverage** (80%+ target)
8. **Run tests frequently** during development
9. **Keep tests simple** and focused on one thing
10. **Update tests** when code changes

## Getting Help

- Check test output and stack traces for error details
- Review test documentation and examples
- Use debugger to step through failing tests
- Consult team members for complex testing scenarios
- Refer to framework documentation (xUnit, FluentAssertions, Moq)
