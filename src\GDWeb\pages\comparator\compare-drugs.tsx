import type { NextPage } from "next";
import Head from "next/head";
import Image from "next/image";
import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MultiSelectDropdown } from "@/components/ui/multi-select-dropdown";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart3, 
  TrendingUp, 
  Shield, 
  Calendar, 
  Settings, 
  Target,
  Activity,
  Grid3X3,
  Table as TableIcon,
  Zap,
  TreePine,
  Layers,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Home as HomeIcon,
  Map,
  FlaskConical,
  Scale,
  Database,
  Lightbulb,
  Bot,
  FileText,
  Building2,
  Search,
  Users,
  MessageSquare,
  Globe,
  Download,
  Bookmark,
  Phone,
  Mail,
  Video,
  Filter,
  X,
  <PERSON><PERSON><PERSON>,
  Loader2,
  ExternalLink
} from "lucide-react";
import { Toolt<PERSON>, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem } from "@/components/ui/dropdown-menu";
import { ColumnComparisonChart } from '@/components/ui/charts/column-comparison';
import { EfficacyHeatmapChart } from '@/components/ui/charts/efficacy-heatmap';
import { RankedColumnChart } from '@/components/ui/charts/ranked-column';
import { ResponseTimelineChart } from '@/components/ui/charts/response-timeline';
import { SafetyEventMatrix } from '@/components/ui/charts/safety-event-matrix';
import { HorizontalBarSafety } from '@/components/ui/charts/horizontal-bar-safety';
import { ForestPlot } from '@/components/ui/charts/forest-plot';
import { CumulativeIncidence } from '@/components/ui/charts/cumulative-incidence';
import navigationConfig from "@/data/navigation-config.json";

const CompareDrugs: NextPage = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedSections, setExpandedSections] = useState<{[key: number]: boolean}>({
    2: true // Clinical Trials Comparator expanded
  });
  const [consultantDropdownOpen, setConsultantDropdownOpen] = useState(false);
  const [diseaseDropdownOpen, setDiseaseDropdownOpen] = useState(false);

  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [efficacyData, setEfficacyData] = useState<any[]>([]);
  const [safetyData, setSafetyData] = useState<any[]>([]);

  // Filter states
  const [showChart, setShowChart] = useState(false);
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [chartConfigOpen, setChartConfigOpen] = useState(false);

  // Chart configuration states
  const [columnComparisonConfig, setColumnComparisonConfig] = useState({
    placeboCorrection: 'side-by-side' as 'corrected' | 'side-by-side',
    showErrorBars: false,
    showPValues: false,
    sortOrder: 'drug-name' as 'drug-name' | 'endpoint'
  });

  // Save analysis states
  const [saveAnalysisName, setSaveAnalysisName] = useState('');
  const [saveAnalysisSuccess, setSaveAnalysisSuccess] = useState(false);

  const [rankedColumnConfig, setRankedColumnConfig] = useState({
    sortOrder: 'drug-name' as 'drug-name' | 'timeline'
  });

  const [responseTimelineConfig, setResponseTimelineConfig] = useState({
    showDataGaps: true
  });

  const [efficacyHeatmapConfig, setEfficacyHeatmapConfig] = useState({
    sortOrder: 'drug-name' as 'drug-name' | 'timeline'
  });

  // Data filter states
  const [comparisonType, setComparisonType] = useState<'efficacy' | 'safety'>('efficacy');
  const [chartConfig, setChartConfig] = useState<any>({});
  const [filters, setFilters] = useState({
    moleculeType: [] as string[],
    mechanismOfAction: [] as string[],
    assetName: [] as string[],
    indication: [] as string[],
    phase: [] as string[],
    trialName: [] as string[],
    umbrellaEndpoints: [] as string[],
    endpoints: [] as string[],
    timepoint: [] as string[],
    dataHandling: [] as string[],
    eventType: [] as string[]
  });

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(25);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingProgress(10);

        // Simulate loading time and import data
        const efficacyModule = await import('@/data/comparator/efficacy-data');
        setLoadingProgress(40);

        const safetyModule = await import('@/data/comparator/safety-data');
        setLoadingProgress(70);

        // Set the imported data
        setEfficacyData(efficacyModule.efficacyData);
        setSafetyData(safetyModule.safetyData);
        setLoadingProgress(100);

        // Small delay to show completion
        setTimeout(() => {
          setIsLoading(false);
        }, 300);
      } catch (error) {
        console.error('Error loading data:', error);
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const toggleSection = (index: number) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Helper function to get icon component by name
  const getIconComponent = (iconName: string) => {
    const icons = {
      HomeIcon: <HomeIcon className="w-4 h-4" />,
      Map: <Map className="w-4 h-4" />,
      Calendar: <Calendar className="w-4 h-4" />,
      Target: <Target className="w-4 h-4" />,
      BarChart3: <BarChart3 className="w-4 h-4" />,
      FlaskConical: <FlaskConical className="w-4 h-4" />,
      Scale: <Scale className="w-4 h-4" />,
      Users: <Users className="w-4 h-4" />,
      MessageSquare: <MessageSquare className="w-4 h-4" />,
      Settings: <Settings className="w-4 h-4" />,
      Database: <Database className="w-4 h-4" />,
      Activity: <Activity className="w-4 h-4" />,
      Lightbulb: <Lightbulb className="w-4 h-4" />,
      Grid3X3: <Grid3X3 className="w-4 h-4" />,
      Search: <Search className="w-4 h-4" />,
      TrendingUp: <TrendingUp className="w-4 h-4" />,
      Building2: <Building2 className="w-4 h-4" />,
      Globe: <Globe className="w-4 h-4" />,
      Bot: <Bot className="w-4 h-4" />,
      FileText: <FileText className="w-4 h-4" />
    };
    return icons[iconName as keyof typeof icons] || <HomeIcon className="w-4 h-4" />;
  };

  const navigationItems = navigationConfig.navigationItems.map(item => ({
    ...item,
    icon: getIconComponent(item.icon),
    children: item.children?.map(child => ({
      ...child,
      icon: getIconComponent(child.icon)
    }))
  })) as Array<{
    icon: React.ReactElement;
    label: string;
    active?: boolean;
    href?: string;
    children?: Array<{
      icon: React.ReactElement;
      label: string;
      href?: string;
    }>;
  }>;

  // Get current dataset based on comparison type
  const currentData = comparisonType === 'efficacy' ? efficacyData : safetyData;

  // Function to get unique values for a field
  const getUniqueValues = (data: any[], field: string) => {
    const values = data.map(item => item[field]).filter(value => value && value !== 'N/A');
    return [...new Set(values)].sort();
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters, comparisonType]);

  // Get filtered data based on current filters
  const filteredData = useMemo(() => {
    return currentData.filter(item => {
      return (
        (filters.moleculeType.length === 0 || filters.moleculeType.includes(item.molecule_type)) &&
        (filters.mechanismOfAction.length === 0 || filters.mechanismOfAction.includes(item.mechanism_of_action)) &&
        (filters.assetName.length === 0 || filters.assetName.includes(item.asset)) &&
        (filters.indication.length === 0 || filters.indication.includes(item.indication)) &&
        (filters.phase.length === 0 || filters.phase.includes(item.trial_phase)) &&
        (filters.trialName.length === 0 || filters.trialName.includes(item.trial_name)) &&
        (filters.umbrellaEndpoints.length === 0 || (comparisonType === 'efficacy' && filters.umbrellaEndpoints.includes(item.umbrella_endpoint))) &&
        (filters.endpoints.length === 0 || (comparisonType === 'efficacy' && filters.endpoints.includes(item.endpoint))) &&
        (filters.timepoint.length === 0 || filters.timepoint.includes(item.timepoint)) &&
        (filters.dataHandling.length === 0 || filters.dataHandling.includes(item.data_handling)) &&
        (filters.eventType.length === 0 || (comparisonType === 'safety' && filters.eventType.includes(item.event_type)))
      );
    });
  }, [currentData, filters, comparisonType]);

  // Get available filter options based on current filters
  const filterOptions = useMemo(() => {
    // Start with all data, then progressively filter based on other selections
    let baseData = currentData;

    // Apply filters one by one to get interdependent options
    const getFilteredOptions = (excludeFilter: string) => {
      return baseData.filter(item => {
        return (
          (excludeFilter === 'moleculeType' || filters.moleculeType.length === 0 || filters.moleculeType.includes(item.molecule_type)) &&
          (excludeFilter === 'mechanismOfAction' || filters.mechanismOfAction.length === 0 || filters.mechanismOfAction.includes(item.mechanism_of_action)) &&
          (excludeFilter === 'assetName' || filters.assetName.length === 0 || filters.assetName.includes(item.asset)) &&
          (excludeFilter === 'indication' || filters.indication.length === 0 || filters.indication.includes(item.indication)) &&
          (excludeFilter === 'phase' || filters.phase.length === 0 || filters.phase.includes(item.trial_phase)) &&
          (excludeFilter === 'trialName' || filters.trialName.length === 0 || filters.trialName.includes(item.trial_name)) &&
          (excludeFilter === 'umbrellaEndpoints' || filters.umbrellaEndpoints.length === 0 || (comparisonType === 'efficacy' && filters.umbrellaEndpoints.includes(item.umbrella_endpoint))) &&
          (excludeFilter === 'endpoints' || filters.endpoints.length === 0 || (comparisonType === 'efficacy' && filters.endpoints.includes(item.endpoint))) &&
          (excludeFilter === 'timepoint' || filters.timepoint.length === 0 || filters.timepoint.includes(item.timepoint)) &&
          (excludeFilter === 'dataHandling' || filters.dataHandling.length === 0 || filters.dataHandling.includes(item.data_handling)) &&
          (excludeFilter === 'eventType' || filters.eventType.length === 0 || (comparisonType === 'safety' && filters.eventType.includes(item.event_type)))
        );
      });
    };

    return {
      moleculeType: getUniqueValues(getFilteredOptions('moleculeType'), 'molecule_type'),
      mechanismOfAction: getUniqueValues(getFilteredOptions('mechanismOfAction'), 'mechanism_of_action'),
      assetName: getUniqueValues(getFilteredOptions('assetName'), 'asset'),
      indication: getUniqueValues(getFilteredOptions('indication'), 'indication'),
      phase: getUniqueValues(getFilteredOptions('phase'), 'trial_phase'),
      trialName: getUniqueValues(getFilteredOptions('trialName'), 'trial_name'),
      umbrellaEndpoints: comparisonType === 'efficacy' ? getUniqueValues(getFilteredOptions('umbrellaEndpoints'), 'umbrella_endpoint') : [],
      endpoints: comparisonType === 'efficacy' ? getUniqueValues(getFilteredOptions('endpoints'), 'endpoint') : [],
      timepoint: getUniqueValues(getFilteredOptions('timepoint'), 'timepoint'),
      dataHandling: getUniqueValues(getFilteredOptions('dataHandling'), 'data_handling'),
      eventType: comparisonType === 'safety' ? getUniqueValues(getFilteredOptions('eventType'), 'event_type') : []
    };
  }, [currentData, filters, comparisonType]);

  // Handle filter changes
  const handleFilterChange = (filterType: keyof typeof filters, values: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  // Chart availability logic
  const getChartAvailability = () => {
    const uniqueAssets = [...new Set(filteredData.map(item => item.asset))];
    const uniqueEndpoints = comparisonType === 'efficacy' 
      ? [...new Set(filteredData.map(item => item.endpoint).filter(Boolean))]
      : [...new Set(filteredData.map(item => item.event_type).filter(Boolean))];
    const uniqueTimepoints = [...new Set(filteredData.map(item => item.timepoint_week))];

    return {
      columnComparison: uniqueAssets.length < 10,
      rankedColumn: uniqueEndpoints.length === 1,
      responseTimeline: uniqueEndpoints.length === 1,
      efficacyHeatmap: true, // Always available
	  safetyMatrix: true, // Always available - display requirements handled in component
	  horizontalBar: true, // Always available - display requirements handled in component  
	  forestPlot: true // Always available
    };
  };

  const chartAvailability = getChartAvailability();

  // Generate chart title
  const generateChartTitle = () => {
    const selectedEndpoint = comparisonType === 'efficacy' 
      ? filters.endpoints[0] || 'All Endpoints'
      : filters.eventType[0] || 'All Event Types';

    const selectedAssets = filters.assetName.length > 0 
      ? filters.assetName.length === 1 
        ? filters.assetName[0] 
        : `${filters.assetName.length} Assets`
      : 'All Assets';

    return `${selectedEndpoint} - ${selectedAssets}`;
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilters({
      moleculeType: [],
      mechanismOfAction: [],
      assetName: [],
      indication: [],
      phase: [],
      trialName: [],
      umbrellaEndpoints: [],
      endpoints: [],
      timepoint: [],
      dataHandling: [],
      eventType: []
    });
    setShowChart(false);
  };

  // Reset filters when comparison type changes (preserve asset and trial selections)
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      moleculeType: [],
      mechanismOfAction: [],
      indication: [],
      phase: [],
      umbrellaEndpoints: [],
      endpoints: [],
      timepoint: [],
      dataHandling: []
      // Keep assetName and trialName intact
    }));
  }, [comparisonType]);

  const handleChartConfigChange = (chartType: string, key: string, value: boolean | string) => {
    switch (chartType) {
      case 'column-comparison':
        setColumnComparisonConfig(prev => ({ ...prev, [key]: value }));
        break;
      case 'ranked-column':
        setRankedColumnConfig(prev => ({ ...prev, [key]: value }));
        break;
      case 'response-timeline':
        setResponseTimelineConfig(prev => ({ ...prev, [key]: value }));
        break;
      case 'efficacy-heatmap':
        setEfficacyHeatmapConfig(prev => ({ ...prev, [key]: value }));
        break;
    }
  };

  const setAsDefaultChart = () => {
    const currentConfig = {
      'column-comparison': columnComparisonConfig,
      'ranked-column': rankedColumnConfig,
      'response-timeline': responseTimelineConfig,
      'efficacy-heatmap': efficacyHeatmapConfig
    };

    localStorage.setItem('defaultChartConfig', JSON.stringify({
      type: selectedChartType,
      config: currentConfig[selectedChartType as keyof typeof currentConfig]
    }));
    alert('Chart configuration saved as default');
  };

  const generateChart = () => {
    if (comparisonType === 'efficacy') {
      setSelectedChartType('column-comparison'); // Set default to first chart
    } else {
      setSelectedChartType('safety-event-matrix'); // Set default to first safety chart
    }
    setShowChart(true);

    // Scroll to chart section after a brief delay to allow state update
    setTimeout(() => {
      const chartSection = document.querySelector('[data-chart-section]');
      if (chartSection) {
        chartSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Get all columns from the current dataset in specified order
  const getTableColumns = () => {
    if (filteredData.length === 0) return [];

    // Define column order
    const primaryCols = [
      'asset', 'trial_name', 'trial_phase', 'indication', 'sub_group', 
      'portion_of_study', 'dose', 'dosing_regimen', 'roa'
    ];

    // Add endpoint/event specific columns
    const endpointCols = comparisonType === 'efficacy' 
      ? ['umbrella_endpoint', 'endpoint', 'data_handling']
      : ['event_type'];

    // Get all available columns
    const allColumns = Object.keys(filteredData[0]);

    // Get remaining columns (excluding primary and endpoint cols)
    const remainingCols = allColumns.filter(col => 
      !primaryCols.includes(col) && 
      !endpointCols.includes(col) && 
      col !== 'results_link'
    );

    // Return ordered columns with Results at the end
    return [...primaryCols, ...endpointCols, ...remainingCols, 'results_link'];
  };

  // Export functionality
  const exportToCSV = () => {
    if (filteredData.length === 0) return;

    const headers = getTableColumns().map(col => 
      col === 'results_link' ? 'Results' : 
      col.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    );

    const csvContent = [
      headers.join(','),
      ...filteredData.map(row => 
        getTableColumns().map(col => {
          const value = row[col];
          if (col === 'results_link') {
            return value && value !== '-' && value !== 'N/A' ? value : '';
          }
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value || '';
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${comparisonType}-comparison-data.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = () => {
    alert('Excel export functionality would be implemented with a library like xlsx. For now, please use CSV export.');
  };

  // Calculate pagination
  const totalPages = recordsPerPage === -1 ? 1 : Math.ceil(filteredData.length / recordsPerPage);
  const startIndex = recordsPerPage === -1 ? 0 : (currentPage - 1) * recordsPerPage;
  const endIndex = recordsPerPage === -1 ? filteredData.length : startIndex + recordsPerPage;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  // Handle page navigation
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // Handle pagination change
  const handlePaginationChange = (newRecordsPerPage: number) => {
    setRecordsPerPage(newRecordsPerPage);
    setCurrentPage(1);
  };

  // Function to get a random sample of sources
  const getRandomSampleSources = (count = 3) => {
    const allSources = filteredData.map(item => ({
        id: item.id,
        results_link: item.results_link,
        results_publication: item.results_publication
    })).filter(item => item.results_link); // Filter out items without source links

    const shuffled = [...allSources].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, shuffled.length));
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Compare Specific Drugs | IBD CI Solution</title>
          <meta name="description" content="Compare specific drugs and analyze competitive intelligence data" />
        </Head>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 text-center space-y-6 max-w-md mx-4">
              <div className="relative">
                <Loader2 className="w-16 h-16 text-blue-600 animate-spin mx-auto" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
              </div>
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-gray-900">Loading Clinical Trial Data</h2>
                <p className="text-gray-600">Preparing efficacy and safety comparisons...</p>
                <div className="w-80 mx-auto">
                  <Progress value={loadingProgress} className="h-2" />
                </div>
                <p className="text-sm text-gray-500">{loadingProgress}% Complete</p>
              </div>
            </div>
          </div>
        )}

        <div className="flex">
          {/* Sidebar */}
          <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 border-r bg-white border-gray-200 flex flex-col`}>
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                {sidebarOpen ? (
                  <div className="flex items-center space-x-3">
                    <Image 
                      src="/gd-logo.png" 
                      alt="GlobalData" 
                      width={120}
                      height={32}
                      className="h-8 w-auto"
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-8">
                    <Image 
                      src="/gd-icon.png" 
                      alt="GlobalData" 
                      width={32}
                      height={32}
                      className="h-8 w-8"
                    />
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="text-gray-600 hover:bg-gray-100"
                >
                  {sidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              <nav className="space-y-2">
                {navigationItems.map((item, index) => (
                  <div key={index}>
                    {!sidebarOpen ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div 
                            className={`flex items-center justify-center px-3 py-2 rounded-md cursor-pointer transition-colors ${
                              item.label === 'Clinical Trials Comparator'
                                ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                                : 'text-gray-700 hover:bg-gray-100'
                            }`}
                            onClick={() => item.children && sidebarOpen && toggleSection(index)}
                          >
                            {item.icon}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="ml-2">
                          <p>{item.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <div 
                        className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                          item.label === 'Clinical Trials Comparator'
                            ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => {
                          if (item.label === 'Home') {
                            window.location.href = '/';
                          } else if ('href' in item && item.href) {
                            if (item.href !== '/comparator') {
                              window.location.href = item.href;
                            }
                          } else if (item.children && sidebarOpen) {
                            toggleSection(index);
                          }
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          {item.icon}
                          {sidebarOpen && <span className="text-xs font-medium">{item.label}</span>}
                        </div>
                        {item.children && sidebarOpen && (
                          expandedSections[index] ? 
                            <ChevronUp className="w-4 h-4" /> : 
                            <ChevronDown className="w-4 h-4" />
                        )}
                      </div>
                    )}
                    {item.children && sidebarOpen && expandedSections[index] && (
                      <div className="ml-6 mt-2 space-y-1">
                        {item.children.map((child, childIndex) => (
                          <div 
                            key={childIndex} 
                            className={`flex items-center space-x-3 px-3 py-1 rounded-md cursor-pointer hover:bg-gray-50 ${
                              child.label === 'Trial Results Comparator' 
                                ? 'text-blue-700 bg-blue-50 font-medium' 
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                            onClick={() => {
                              if (child.href) {
                                window.location.href = child.href;
                              }
                            }}
                          >
                            {child.icon}
                            <span className="text-xs">{child.label}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>

              {/* Document Library Section */}
              {sidebarOpen && navigationConfig.documentLibrary && (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mx-2">
                    <div className="mb-2">
                      <h3 className="text-xs font-semibold text-blue-800 uppercase tracking-wide">{navigationConfig.documentLibrary.title}</h3>
                    </div>
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-blue-700 hover:text-blue-900 hover:bg-blue-100 transition-colors">
                      {navigationConfig.documentLibrary.link?.icon && getIconComponent(navigationConfig.documentLibrary.link.icon)}
                      <span className="text-xs font-medium">{navigationConfig.documentLibrary.link?.label}</span>
                    </div>
                  </div>

                  {/* Feedback Section */}
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors mx-2">
                      <MessageSquare className="w-4 h-4" />
                      <span className="text-xs font-medium">Help us improve this solution</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 px-8 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.href = '/comparator'}
                        className="text-gray-600 hover:text-gray-900 p-1"
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Back to Comparator
                      </Button>
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900">Compare Specific Drugs</h1>
                    <p className="text-sm text-gray-600 mt-1">Compare drug profiles and characteristics across clinical trials</p>
                  </div>
                  <div className="relative">
                    <div 
                      className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full cursor-pointer hover:bg-blue-200 transition-colors"
                      onClick={() => setDiseaseDropdownOpen(!diseaseDropdownOpen)}
                    >
                      <span className="text-sm font-medium text-blue-800">IBD</span>
                      <ChevronDown className={`w-3 h-3 text-blue-600 transition-transform ${diseaseDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </div>
                </div>

                {/* Solution Consultant */}
                <div className="relative">
                  <div 
                    className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors border border-gray-200 bg-white shadow-sm"
                    onClick={() => {
                      setConsultantDropdownOpen(!consultantDropdownOpen);
                      setDiseaseDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      {/* Fake headshot */}
                      <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                        <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500 font-medium">Your Solution Consultant</div>
                        <div className="text-sm font-semibold text-gray-900">James Davidson</div>
                      </div>
                      <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${consultantDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-8 py-8 space-y-8">
              {/* Filters Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Filter className="w-5 h-5 mr-2" />
                    Filter Selection
                  </CardTitle>
                  <CardDescription>
                    Configure your comparison parameters using the filters below
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Asset Selection Row */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-3">
                      <div className="flex items-center space-x-2">
                        <FlaskConical className="w-5 h-5 text-blue-600" />
                        <h3 className="font-medium text-gray-900">Asset Selection</h3>
                      </div>
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Molecule Type"
                        options={filterOptions.moleculeType}
                        selected={filters.moleculeType}
                        onChange={(values) => handleFilterChange('moleculeType', values)}
                      />
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Mechanism of Action"
                        options={filterOptions.mechanismOfAction}
                        selected={filters.mechanismOfAction}
                        onChange={(values) => handleFilterChange('mechanismOfAction', values)}
                      />
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Asset Name"
                        options={filterOptions.assetName}
                        selected={filters.assetName}
                        onChange={(values) => handleFilterChange('assetName', values)}
                      />
                    </div>
                  </div>

                  {/* Trial Selection Row */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-3">
                      <div className="flex items-center space-x-2">
                        <Activity className="w-5 h-5 text-green-600" />
                        <h3 className="font-medium text-gray-900">Trial Selection</h3>
                      </div>
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Indication"
                        options={filterOptions.indication}
                        selected={filters.indication}
                        onChange={(values) => handleFilterChange('indication', values)}
                      />
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Phase"
                        options={filterOptions.phase}
                        selected={filters.phase}
                        onChange={(values) => handleFilterChange('phase', values)}
                      />
                    </div>
                    <div className="col-span-3">
                      <MultiSelectDropdown
                        label="Trial Name"
                        options={filterOptions.trialName}
                        selected={filters.trialName}
                        onChange={(values) => handleFilterChange('trialName', values)}
                      />
                    </div>
                  </div>

                  {/* Comparison Type Row */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-3">
                      <div className="flex items-center space-x-2">
                        <Scale className="w-5 h-5 text-purple-600" />
                        <h3 className="font-medium text-gray-900">Comparison Type</h3>
                      </div>
                    </div>
                    <div className="col-span-9">
                      <div className="flex items-center space-x-6">
                        <label className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="radio"
                            name="comparisonType"
                            value="efficacy"
                            checked={comparisonType === 'efficacy'}
                            onChange={(e) => setComparisonType(e.target.value as 'efficacy' | 'safety')}
                            className="text-purple-600"
                          />
                          <span className="text-sm text-gray-700">Efficacy Comparison</span>
                        </label>
                        <label className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="radio"
                            name="comparisonType"
                            value="safety"
                            checked={comparisonType === 'safety'}
                            onChange={(e) => setComparisonType(e.target.value as 'efficacy' | 'safety')}
                            className="text-purple-600"
                          />
                          <span className="text-sm text-gray-700">Safety Comparison</span>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Efficacy Data Row - Conditional */}
                  {comparisonType === 'efficacy' && (
                    <>
                      <div className="grid grid-cols-12 gap-4 items-center">
                        <div className="col-span-3">
                          <div className="flex items-center space-x-2">
                            <Target className="w-5 h-5 text-orange-600" />
                            <h3 className="font-medium text-gray-900">Efficacy Data</h3>
                          </div>
                        </div>
                        <div className="col-span-3">
                          <MultiSelectDropdown
                            label="Umbrella Endpoints"
                            options={filterOptions.umbrellaEndpoints}
                            selected={filters.umbrellaEndpoints}
                            onChange={(values) => handleFilterChange('umbrellaEndpoints', values)}
                          />
                        </div>
                        <div className="col-span-3">
                          <MultiSelectDropdown
                            label="Endpoints"
                            options={filterOptions.endpoints}
                            selected={filters.endpoints}
                            onChange={(values) => handleFilterChange('endpoints', values)}
                          />
                        </div>
                        <div className="col-span-3">
                          <MultiSelectDropdown
                            label="Timepoint"
                            options={filterOptions.timepoint}
                            selected={filters.timepoint}
                            onChange={(values) => handleFilterChange('timepoint', values)}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-12 gap-4 items-center">
                        <div className="col-span-3">
                          {/* Empty space for alignment */}
                        </div>
                        <div className="col-span-3">
                          <MultiSelectDropdown
                            label="Data Handling"
                            options={filterOptions.dataHandling}
                            selected={filters.dataHandling}
                            onChange={(values) => handleFilterChange('dataHandling', values)}
                          />
                        </div>
                        <div className="col-span-6">
                          {/* Empty space for alignment */}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Safety Data Row - Conditional */}
                  {comparisonType === 'safety' && (
                    <div className="grid grid-cols-12 gap-4 items-center">
                      <div className="col-span-3">
                        <div className="flex items-center space-x-2">
                          <Shield className="w-5 h-5 text-red-600" />
                          <h3 className="font-medium text-gray-900">Safety Data</h3>
                        </div>
                      </div>
                      <div className="col-span-3">
                        <MultiSelectDropdown
                          label="Event Type"
                          options={filterOptions.eventType}
                          selected={filters.eventType}
                          onChange={(values) => handleFilterChange('eventType', values)}
                        />
                      </div>
                      <div className="col-span-3">
                        <MultiSelectDropdown
                          label="Timepoint"
                          options={filterOptions.timepoint}
                          selected={filters.timepoint}
                          onChange={(values) => handleFilterChange('timepoint', values)}
                        />
                      </div>
                      <div className="col-span-3">
                        {/* Empty space for alignment */}
                      </div>
                    </div>
                  )}

                  {/* Filter Summary and Actions */}
                  <div className="border-t border-gray-200 pt-4 mt-6">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        {filteredData.length} comparator{filteredData.length !== 1 ? 's' : ''} found
                      </div>

                      <div className="flex items-center space-x-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearAllFilters}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Clear All
                        </Button>

                        <Button
                          onClick={generateChart}
                          disabled={filteredData.length === 0}
                          className="min-w-[140px]"
                        >
                          <BarChart3 className="w-4 h-4 mr-2" />
                          Generate Chart
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Chart Preview Section - Efficacy */}
              {!showChart && comparisonType === 'efficacy' && filteredData.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="w-5 h-5 mr-2" />
                      Available Chart Options
                    </CardTitle>
                    <CardDescription>
                      Click &quot;Generate Chart&quot; to show charts with your filtered data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-4">
                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.columnComparison ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <BarChart3 className={`w-5 h-5 ${chartAvailability.columnComparison ? 'text-blue-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.columnComparison ? 'text-blue-900' : 'text-gray-500'}`}>Column Comparison</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.columnComparison ? 'text-blue-600' : 'text-gray-400'}`}>
                          {chartAvailability.columnComparison ? 'Available' : 'Requires < 10 comparators'}
                        </p>
                      </div>

                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.rankedColumn ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <BarChart3 className={`w-5 h-5 ${chartAvailability.rankedColumn ? 'text-green-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.rankedColumn ? 'text-green-900' : 'text-gray-500'}`}>Ranked Bar</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.rankedColumn ? 'text-green-600' : 'text-gray-400'}`}>
                          {chartAvailability.rankedColumn ? 'Available' : 'Requires single endpoint'}
                        </p>
                      </div>

                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.responseTimeline ? 'border-purple-200 bg-purple-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <TrendingUp className={`w-5 h-5 ${chartAvailability.responseTimeline ? 'text-purple-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.responseTimeline ? 'text-purple-900' : 'text-gray-500'}`}>Response Timeline</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.responseTimeline ? 'text-purple-600' : 'text-gray-400'}`}>
                          {chartAvailability.responseTimeline ? 'Available' : 'Requires single endpoint'}
                        </p>
                      </div>

                      <div className="p-4 rounded-lg border-2 border-orange-200 bg-orange-50">
                        <div className="flex items-center space-x-2 mb-2">
                          <Grid3X3 className="w-5 h-5 text-orange-600" />
                          <span className="font-medium text-orange-900">Efficacy Heatmap</span>
                        </div>
                        <p className="text-xs text-orange-600">
                          Available
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Chart Preview Section - Safety */}
              {!showChart && comparisonType === 'safety' && filteredData.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="w-5 h-5 mr-2" />
                      Available Safety Chart Options
                    </CardTitle>
                    <CardDescription>
                      Click &quot;Generate Chart&quot; to show safety charts with your filtered data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-4">
                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.safetyMatrix ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <Grid3X3 className={`w-5 h-5 ${chartAvailability.safetyMatrix ? 'text-red-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.safetyMatrix ? 'text-red-900' : 'text-gray-500'}`}>Safety Event Matrix</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.safetyMatrix ? 'text-red-600' : 'text-gray-400'}`}>
                          {chartAvailability.safetyMatrix ? 'Available' : 'Requires < 10 comparators'}
                        </p>
                      </div>

                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.horizontalBar ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <BarChart3 className={`w-5 h-5 ${chartAvailability.horizontalBar ? 'text-blue-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.horizontalBar ? 'text-blue-900' : 'text-gray-500'}`}>Ranked Bar Chart</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.horizontalBar ? 'text-blue-600' : 'text-gray-400'}`}>
                          {chartAvailability.horizontalBar ? 'Available' : 'Single endpoint & timepoint required'}
                        </p>
                      </div>

                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.forestPlot ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <Activity className={`w-5 h-5 ${chartAvailability.forestPlot ? 'text-green-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.forestPlot ? 'text-green-900' : 'text-gray-500'}`}>Hazard Plot</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.forestPlot ? 'text-green-600' : 'text-gray-400'}`}>
                          Available
                        </p>
                      </div>

                      <div className={`p-4 rounded-lg border-2 ${chartAvailability.columnComparison ? 'border-purple-200 bg-purple-50' : 'border-gray-200 bg-gray-50'}`}>
                        <div className="flex items-center space-x-2 mb-2">
                          <BarChart3 className={`w-5 h-5 ${chartAvailability.columnComparison ? 'text-purple-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${chartAvailability.columnComparison ? 'text-purple-900' : 'text-gray-500'}`}>Column Comparison</span>
                        </div>
                        <p className={`text-xs ${chartAvailability.columnComparison ? 'text-purple-600' : 'text-gray-400'}`}>
                          {chartAvailability.columnComparison ? 'Available' : 'Requires < 10 comparators'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Chart Section */}
              {showChart && comparisonType === 'efficacy' && (
                <Card data-chart-section>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="w-5 h-5 mr-2" />
                      Efficacy Comparison Chart
                    </CardTitle>
                    <CardDescription>
                      Efficacy comparison across selected trials
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={selectedChartType} onValueChange={setSelectedChartType} className="w-full">
                      <TabsList className="grid grid-cols-4 w-full mb-2">
                        <TabsTrigger 
                          value="column-comparison" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.columnComparison}
                        >
                          <BarChart3 className="w-4 h-4" />
                          <span>Column Comparison</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="ranked-column" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.rankedColumn}
                        >
                          <BarChart3 className="w-4 h-4" />
                          <span>Ranked Bar</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="response-timeline" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.responseTimeline}
                        >
                          <TrendingUp className="w-4 h-4" />
                          <span>Response Timeline</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="efficacy-heatmap" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.efficacyHeatmap}
                        >
                          <Grid3X3 className="w-4 h-4" />
                          <span>Efficacy Heatmap</span>
                        </TabsTrigger>
                      </TabsList>

                      {/* Chart Actions */}
                      <div className="flex justify-end mb-2 space-x-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center space-x-2">
                              <Bookmark className="w-4 h-4" />
                              <span>Save Analysis</span>
                              <ChevronDown className="w-3 h-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-64 p-3">
                            <DropdownMenuLabel>Save Analysis</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <div className="space-y-3">
                              <input 
                                type="text" 
                                placeholder="Enter analysis name..." 
                                value={saveAnalysisName}
                                onChange={(e) => setSaveAnalysisName(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                              <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span className="text-sm text-gray-700">Share with your team</span>
                              </label>
                              <Button 
                                size="sm" 
                                className={`w-full flex items-center space-x-2 transition-colors ${
                                  saveAnalysisSuccess 
                                    ? 'bg-green-600 hover:bg-green-700' 
                                    : ''
                                }`}
                                onClick={() => {
                                  if (saveAnalysisName.trim()) {
                                    setSaveAnalysisSuccess(true);
                                    setTimeout(() => {
                                      setSaveAnalysisSuccess(false);
                                      setSaveAnalysisName('');
                                    }, 2000);
                                  }
                                }}
                                disabled={!saveAnalysisName.trim()}
                              >
                                <Bookmark className="w-4 h-4" />
                                <span>{saveAnalysisSuccess ? 'Saved!' : 'Save'}</span>
                              </Button>
                            </div>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <DropdownMenu open={chartConfigOpen} onOpenChange={setChartConfigOpen}>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center space-x-2">
                              <Settings className="w-4 h-4" />
                              <span>Configure</span>
                              <ChevronDown className={`w-3 h-3 transition-transform ${chartConfigOpen ? 'rotate-180' : ''}`} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-64">
                            <DropdownMenuLabel>Chart Configuration</DropdownMenuLabel>
                            <DropdownMenuSeparator />

                            {/* Column Comparison Configuration */}
                            {selectedChartType === 'column-comparison' && (
                              <>
                                <DropdownMenuLabel>Comparator Display</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('column-comparison', 'placeboCorrection', 'side-by-side')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>Show Drug and Comparator</span>
                                    {columnComparisonConfig.placeboCorrection === 'side-by-side' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('column-comparison', 'placeboCorrection', 'corrected')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>Show Placebo-Corrected</span>
                                    {columnComparisonConfig.placeboCorrection === 'corrected' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuLabel>Chart Display</DropdownMenuLabel>
                                <DropdownMenuCheckboxItem
                                  checked={columnComparisonConfig.showErrorBars}
                                  onCheckedChange={(checked) => handleChartConfigChange('column-comparison', 'showErrorBars', checked)}
                                  className="flex items-center justify-between w-full"
                                >
                                  <span>Show Error Bars</span>
                                </DropdownMenuCheckboxItem>
                                <DropdownMenuCheckboxItem
                                  checked={columnComparisonConfig.showPValues}
                                  onCheckedChange={(checked) => handleChartConfigChange('column-comparison', 'showPValues', checked)}
                                  className="flex items-center justify-between w-full"
                                >
                                  <span>Show P-values</span>
                                </DropdownMenuCheckboxItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuLabel>Sort Order</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('column-comparison', 'sortOrder', 'drug-name')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Drug Name</span>
                                    {columnComparisonConfig.sortOrder === 'drug-name' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('column-comparison', 'sortOrder', 'endpoint')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Outcome</span>
                                    {columnComparisonConfig.sortOrder === 'endpoint' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                              </>
                            )}

                            {/* Ranked Column Configuration */}
                            {selectedChartType === 'ranked-column' && (
                              <>
                                <DropdownMenuLabel>Sort Order</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('ranked-column', 'sortOrder', 'drug-name')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Drug Name</span>
                                    {rankedColumnConfig.sortOrder === 'drug-name' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('ranked-column', 'sortOrder', 'timeline')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Outcome</span>
                                    {rankedColumnConfig.sortOrder === 'timeline' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                              </>
                            )}

                            {/* Response Timeline Configuration */}
                            {selectedChartType === 'response-timeline' && (
                              <>
                                <DropdownMenuLabel>Chart Display</DropdownMenuLabel>
                                <DropdownMenuCheckboxItem
                                  checked={responseTimelineConfig.showDataGaps}
                                  onCheckedChange={(checked) => handleChartConfigChange('response-timeline', 'showDataGaps', checked)}
                                  className="flex items-center justify-between w-full"
                                >
                                  <span>Show Data Gaps</span>
                                </DropdownMenuCheckboxItem>
                              </>
                            )}

                            {/* Efficacy Heatmap Configuration */}
                            {selectedChartType === 'efficacy-heatmap' && (
                              <>
                                <DropdownMenuLabel>Sort Order</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('efficacy-heatmap', 'sortOrder', 'drug-name')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Drug Name</span>
                                    {efficacyHeatmapConfig.sortOrder === 'drug-name' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleChartConfigChange('efficacy-heatmap', 'sortOrder', 'timeline')}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>By Outcome</span>
                                    {efficacyHeatmapConfig.sortOrder === 'timeline' && <span>✓</span>}
                                  </div>
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <TabsContent value="column-comparison" className="mt-0">
                        <ColumnComparisonChart 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={columnComparisonConfig}
                        />
                      </TabsContent>

                      <TabsContent value="ranked-column" className="mt-0">
                        <RankedColumnChart 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={rankedColumnConfig}
                        />
                      </TabsContent>

                      <TabsContent value="response-timeline" className="mt-0">
                        <ResponseTimelineChart 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={responseTimelineConfig}
                        />
                      </TabsContent>

                      <TabsContent value="efficacy-heatmap" className="mt-0">
                        <EfficacyHeatmapChart 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={efficacyHeatmapConfig}
                        />
                      </TabsContent>
                    </Tabs>

                    {/* Sources Section for Charts */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Data Sources</h4>
                      <div className="text-xs text-gray-600">
                        <a 
                          href="https://acg2024.eventscribe.net/fsPopup.asp?PresentationID=1498111&mode=presInfo" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          ACG 2024
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://ueg.eu/library/two-year-efficacy-and-safety-of-MIR-following-104-weeks-of-continuous-treatment-interim-results-from-the-lucent-3-open-label-extension-study/85233adc-743b-11ee-921f-0242ac140004" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          UEGW 2023
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://www.thelancet.com/journals/lancet/article/PIIS0140-6736(21)00666-8/fulltext" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          The Lancet 2021
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Safety Chart Section */}
              {showChart && comparisonType === 'safety' && (
                <Card data-chart-section>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="w-5 h-5 mr-2" />
                      Safety Comparison Chart
                    </CardTitle>
                    <CardDescription>
                      Safety comparison across selected trials
                    </CardDescription>
                  </CardHeader>
                  <CardContent>

<Tabs value={selectedChartType} onValueChange={setSelectedChartType} className="w-full">
                      {/* Safety Chart Actions */}
                      <div className="flex justify-end mb-2 space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="flex items-center space-x-2"
                          onClick={() => {
                            const name = prompt('Enter a name for this saved visualization:');
                            if (name) {
                              alert(`Visualization "${name}" saved successfully!`);
                            }
                          }}
                        >
                          <Bookmark className="w-4 h-4" />
                          <span>Save</span>
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center space-x-2">
                              <Download className="w-4 h-4" />
                              <span>Export</span>
                              <ChevronDown className="w-3 h-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Export Chart</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => alert('Exporting as PNG...')}>
                              Export as PNG
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => alert('Exporting as PDF...')}>
                              Export as PDF
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => alert('Downloading data...')}>
                              Download Data
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <TabsList className="grid grid-cols-4 w-full mb-2">
                        <TabsTrigger 
                          value="safety-event-matrix" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.safetyMatrix}
                        >
                          <Grid3X3 className="w-4 h-4" />
                          <span>Safety Matrix</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="horizontal-bar-safety" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.horizontalBar}
                        >
                          <BarChart3 className="w-4 h-4" />
                          <span>Ranked Bar Chart</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="forest-plot" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.forestPlot}
                        >
                          <Activity className="w-4 h-4" />
                          <span>Hazard Plot</span>
                        </TabsTrigger>
                        <TabsTrigger 
                          value="column-comparison-safety" 
                          className="flex items-center space-x-2"
                          disabled={!chartAvailability.columnComparison}
                        >
                          <BarChart3 className="w-4 h-4" />
                          <span>Column Comparison</span>
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="safety-event-matrix" className="mt-0">
                        <SafetyEventMatrix 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={{
                            sortOrder: 'severity'
                          }}
                        />
                      </TabsContent>

                      <TabsContent value="horizontal-bar-safety" className="mt-0">
                        <HorizontalBarSafety 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={{
                            sortOrder: 'magnitude'
                          }}
                        />
                      </TabsContent>

                      <TabsContent value="forest-plot" className="mt-0">
                        <ForestPlot 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={{
                            sortOrder: 'effect-size'
                          }}
                        />
                      </TabsContent>

                      <TabsContent value="column-comparison-safety" className="mt-0">
                        <ColumnComparisonChart 
                          data={filteredData} 
                          title={generateChartTitle()}
                          config={columnComparisonConfig}
                        />
                      </TabsContent>
                    </Tabs>

                    {/* Sources Section for Charts */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Data Sources</h4>
                      <div className="text-xs text-gray-600">
                        <a 
                          href="https://acg2024.eventscribe.net/fsPopup.asp?PresentationID=1498111&mode=presInfo" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          ACG 2024
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://ueg.eu/library/two-year-efficacy-and-safety-of-MIR-following-104-weeks-of-continuous-treatment-interim-results-from-the-lucent-3-open-label-extension-study/85233adc-743b-11ee-921f-0242ac140004" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          UEGW 2023
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                        {', '}
                        <a 
                          href="https://www.thelancet.com/journals/lancet/article/PIIS0140-6736(21)00666-8/fulltext" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-blue-600 hover:text-blue-800 underline inline-flex items-center"
                        >
                          The Lancet 2021
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Data Table Section */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center">
                        <TableIcon className="w-5 h-5 mr-2" />
                        {comparisonType === 'efficacy' ? 'Efficacy' : 'Safety'} Data Table
                      </CardTitle>
                      <CardDescription>
                        Detailed comparison of selected {comparisonType} data ({filteredData.length} records)
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm" className="flex items-center space-x-2">
                            <Settings className="w-4 h-4" />
                            <span>Configure Columns</span>
                            <ChevronDown className="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-64">
                          <DropdownMenuLabel>Configure Columns</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          {getTableColumns().map((column) => (
                            <DropdownMenuCheckboxItem
                              key={column}
                              checked={true}
                              onCheckedChange={() => {}}
                            >
                              {column === 'results_link' ? 'Results' : 
                               column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </DropdownMenuCheckboxItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm" className="flex items-center space-x-2">
                            <Download className="w-4 h-4" />
                            <span>Export</span>
                            <ChevronDown className="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Export Data</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={exportToCSV}>
                            <FileText className="w-4 h-4 mr-2" />
                            Export as CSV
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={exportToExcel}>
                            <Grid3X3 className="w-4 h-4 mr-2" />
                            Export as Excel
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <div className="flex items-center space-x-2">
                              <span>Visible Columns Only</span>
                              <span className="ml-auto">✓</span>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            All Columns
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Top Pagination and Scroll Controls */}
                  {filteredData.length > 0 && (
                    <div className="space-y-4 pb-4 border-b border-gray-200 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm text-gray-600">
                            Showing {recordsPerPage === -1 ? 'all' : `${startIndex + 1} to ${Math.min(endIndex, filteredData.length)}`} of {filteredData.length} records
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm" className="flex items-center space-x-2">
                                <span>Show: {recordsPerPage === -1 ? 'All' : recordsPerPage}</span>
                                <ChevronDown className="w-3 h-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start">
                              <DropdownMenuLabel>Records per page</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handlePaginationChange(25)}>
                                <div className="flex items-center space-x-2">
                                  <span>25</span>
                                  {recordsPerPage === 25 && <span className="ml-auto">✓</span>}
                                </div>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handlePaginationChange(50)}>
                                <div className="flex items-center space-x-2">
                                  <span>50</span>
                                  {recordsPerPage === 50 && <span className="ml-auto">✓</span>}
                                </div>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handlePaginationChange(-1)}>
                                <div className="flex items-center space-x-2">
                                  <span>Show All</span>
                                  {recordsPerPage === -1 && <span className="ml-auto">✓</span>}
                                </div>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {recordsPerPage !== -1 && (
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => goToPage(currentPage - 1)}
                              disabled={currentPage === 1}
                            >
                              <ChevronLeft className="w-4 h-4 mr-1" />
                              Previous
                            </Button>

                            <div className="flex items-center space-x-1">
                              {/* Show first page */}
                              {currentPage > 3 && (
                                <>
                                  <Button
                                    variant={currentPage === 1 ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => goToPage(1)}
                                    className="w-8 h-8 p-0"
                                  >
                                    1
                                  </Button>
                                  {currentPage > 4 && <span className="text-gray-400">...</span>}
                                </>
                              )}

                              {/* Show pages around current page */}
                              {Array.from({ length: totalPages }, (_, i) => i + 1)
                                .filter(page => page >= currentPage - 2 && page <= currentPage + 2)
                                .map(page => (
                                  <Button
                                    key={page}
                                    variant={currentPage === page ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => goToPage(page)}
                                    className="w-8 h-8 p-0"
                                  >
                                    {page}
                                  </Button>
                                ))}

                              {/* Show last page */}
                              {currentPage < totalPages - 2 && (
                                <>
                                  {currentPage < totalPages - 3 && <span className="text-gray-400">...</span>}
                                  <Button
                                    variant={currentPage === totalPages ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => goToPage(totalPages)}
                                    className="w-8 h-8 p-0"
                                  >
                                    {totalPages}
                                  </Button>
                                </>
                              )}
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => goToPage(currentPage + 1)}
                              disabled={currentPage === totalPages}
                            >
                              Next
                              <ChevronRight className="w-4 h-4 ml-1" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="overflow-x-auto">
                    

                    <Table>
                      <TableHeader>
                        <TableRow>
                          {getTableColumns().map((column) => (
                            <TableHead key={column} className="whitespace-nowrap">
                              <div className="flex items-center space-x-2">
                                <span>
                                  {column === 'results_link' ? 'Results' : 
                                   column.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                </span>
                                <Filter className="w-3 h-3 text-gray-400 hover:text-gray-600 cursor-pointer" />
                              </div>
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedData.length > 0 ? (
                          paginatedData.map((row, index) => (
                            <TableRow key={startIndex + index}>
                              {getTableColumns().map((column) => (
                                <TableCell key={column} className="whitespace-nowrap">
                                  {column === 'results_link' ? (
                                    row[column] && row[column] !== '-' && row[column] !== 'N/A' ? (
                                      <a 
                                        href={row[column]} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 underline font-medium"
                                      >
                                        Results
                                      </a>
                                    ) : (
                                      <span className="text-gray-400">-</span>
                                    )
                                  ) : (
                                    typeof row[column] === 'number' ? 
                                      row[column].toLocaleString() : 
                                      row[column] || '-'
                                  )}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={getTableColumns().length} className="text-center py-8 text-gray-500">
                              No data matches your current filter selection. Try adjusting your filters.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Bottom Pagination Controls */}
                  {filteredData.length > 0 && recordsPerPage !== -1 && (
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200 mt-4">
                      <div className="text-sm text-gray-600">
                        Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} records
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(currentPage - 1)}
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft className="w-4 h-4 mr-1" />
                          Previous
                        </Button>

                        <div className="flex items-center space-x-1">
                          {/* Show first page */}
                          {currentPage > 3 && (
                            <>
                              <Button
                                variant={currentPage === 1 ? "default" : "outline"}
                                size="sm"
                                onClick={() => goToPage(1)}
                                className="w-8 h-8 p-0"
                              >
                                1
                              </Button>
                              {currentPage > 4 && <span className="text-gray-400">...</span>}
                            </>
                          )}

                          {/* Show pages around current page */}
                          {Array.from({ length: totalPages }, (_, i) => i + 1)
                            .filter(page => page >= currentPage - 2 && page <= currentPage + 2)
                            .map(page => (
                              <Button
                                key={page}
                                variant={currentPage === page ? "default" : "outline"}
                                size="sm"
                                onClick={() => goToPage(page)}
                                className="w-8 h-8 p-0"
                              >
                                {page}
                              </Button>
                            ))}

                          {/* Show last page */}
                          {currentPage < totalPages - 2 && (
                            <>
                              {currentPage < totalPages - 3 && <span className="text-gray-400">...</span>}
                              <Button
                                variant={currentPage === totalPages ? "default" : "outline"}
                                size="sm"
                                onClick={() => goToPage(totalPages)}
                                className="w-8 h-8 p-0"
                              >
                                {totalPages}
                              </Button>
                            </>
                          )}
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(currentPage + 1)}
                          disabled={currentPage === totalPages}
                        >
                          Next
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CompareDrugs;