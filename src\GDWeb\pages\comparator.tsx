import type { NextPage } from "next";
import Head from "next/head";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  BarChart3, 
  TrendingUp, 
  Shield, 
  Calendar, 
  Settings, 
  Target,
  Activity,
  Grid3X3,
  LineChart,
  Table,
  Zap,
  TreePine,
  Layers,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Home as HomeIcon,
  Map,
  FlaskConical,
  Scale,
  Database,
  Lightbulb,
  Bot,
  FileText,
  Building2,
  Search,
  Users,
  MessageSquare,
  Globe,
  Download,
  Bookmark,
  Phone,
  Mail,
  Video
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import navigationConfig from "@/data/navigation-config.json";
import { useRouter } from "next/router";

interface VisualizationThumbnail {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  chartType?: string;
  detailedDescription?: string;
}

interface SavedVisualization {
  id: string;
  name: string;
  type: string;
  description: string;
  sharedBy?: string;
}

const Comparator: NextPage = () => {
  const router = useRouter();
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [selectedVisualization, setSelectedVisualization] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedSections, setExpandedSections] = useState<{[key: number]: boolean}>({
    2: true // Start with Clinical Trials Comparator expanded (index 2)
  });
  const [selectedSavedVisualization, setSelectedSavedVisualization] = useState<string | null>(null);
  const [consultantDropdownOpen, setConsultantDropdownOpen] = useState(false);
  const [diseaseDropdownOpen, setDiseaseDropdownOpen] = useState(false);
  const [savedVisualizationsDropdownOpen, setSavedVisualizationsDropdownOpen] = useState(false);
  const [selectedVisualizationPreview, setSelectedVisualizationPreview] = useState<VisualizationThumbnail | null>(null);

  const toggleSection = (index: number) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Helper function to get icon component by name
  const getIconComponent = (iconName: string) => {
    const icons = {
      HomeIcon: <HomeIcon className="w-4 h-4" />,
      Map: <Map className="w-4 h-4" />,
      Calendar: <Calendar className="w-4 h-4" />,
      Target: <Target className="w-4 h-4" />,
      BarChart3: <BarChart3 className="w-4 h-4" />,
      FlaskConical: <FlaskConical className="w-4 h-4" />,
      Scale: <Scale className="w-4 h-4" />,
      Users: <Users className="w-4 h-4" />,
      MessageSquare: <MessageSquare className="w-4 h-4" />,
      Settings: <Settings className="w-4 h-4" />,
      Database: <Database className="w-4 h-4" />,
      Activity: <Activity className="w-4 h-4" />,
      Lightbulb: <Lightbulb className="w-4 h-4" />,
      Grid3X3: <Grid3X3 className="w-4 h-4" />,
      Search: <Search className="w-4 h-4" />,
      TrendingUp: <TrendingUp className="w-4 h-4" />,
      Building2: <Building2 className="w-4 h-4" />,
      Globe: <Globe className="w-4 h-4" />,
      Bot: <Bot className="w-4 h-4" />,
      FileText: <FileText className="w-4 h-4" />
    };
    return icons[iconName as keyof typeof icons] || <HomeIcon className="w-4 h-4" />;
  };

  const navigationItems = navigationConfig.navigationItems.map(item => ({
    ...item,
    icon: getIconComponent(item.icon),
    children: item.children?.map(child => ({
      ...child,
      icon: getIconComponent(child.icon)
    }))
  }));

  const savedVisualizations: SavedVisualization[] = [
    {
      id: "saved-1",
      name: "Q4 2024 Efficacy Review",
      type: "Bar Chart",
      description: "Response rates across key trials"
    },
    {
      id: "saved-2",
      name: "Safety Profile Comparison",
      type: "Safety Matrix",
      description: "AE frequencies for top 5 drugs"
    },
    {
      id: "saved-3",
      name: "Conference Data Analysis",
      type: "Waterfall Plot",
      description: "ECCO 2024 presentation insights",
      sharedBy: "Sarah Lyon"
    }
  ];

  const versionHistory = [
    {
      id: "v2.3",
      name: "Version 2.3",
      date: "Dec 15, 2024",
      description: "Added ECCO 2024 congress data and updated safety profiles for 12 new trials."
    },
    {
      id: "v2.2",
      name: "Version 2.2",
      date: "Nov 8, 2024",
      description: "Enhanced efficacy endpoints and included biosimilar comparison data."
    },
    {
      id: "v2.1",
      name: "Version 2.1",
      date: "Oct 22, 2024",
      description: "Major update with 15 new trials and improved data validation processes."
    },
    {
      id: "v2.0",
      name: "Version 2.0",
      date: "Sep 30, 2024",
      description: "Complete platform redesign with new visualization capabilities and expanded dataset."
    }
  ];

  const primaryActions = [
    {
      id: "compare-drugs",
      title: "Compare therapy efficacy and safety",
      description: "Side-by-side comparison of treatment efficacy and safety profiles",
      icon: <Target className="w-6 h-6" />,
      color: "bg-blue-50 border-blue-200 hover:bg-blue-100"
    },
    {
      id: "congress-data",
      title: "Review latest congress data",
      description: "Access to the latest presentations and data from scientific congresses and meetings",
      icon: <Calendar className="w-6 h-6" />,
      color: "bg-orange-50 border-orange-200 hover:bg-orange-100",
    },
    {
      id: "saved-visualizations",
      title: "Your Saved Comparators",
      description: "Quick access to your previously saved chart configurations and analysis setups",
      icon: <Bookmark className="w-6 h-6" />,
      color: "bg-indigo-50 border-indigo-200 hover:bg-indigo-100",
      hasSavedOptions: true
    }
  ];

  const preConfiguredAnalyses: VisualizationThumbnail[] = [
    {
      id: "jak-inhibitor-efficacy",
      label: "JAK Inhibitor Efficacy in Crohn's Disease",
      description: "Compare JAK inhibitors' remission rates in CD",
      icon: <BarChart3 className="w-8 h-8 text-blue-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Grouped Bar Chart",
      detailedDescription: "Compares clinical remission rates (CDAI) at Week 12 for all JAK inhibitors (upadacitinib, ritlecitinib, brepocitinib) in CD patients, showing placebo-corrected differences with p-values and error bars. Helps assess class efficacy in induction."
    },
    {
      id: "bio-naive-vs-experienced",
      label: "Bio-Naïve vs Bio-Experienced Response",
      description: "Treatment effects by prior therapy exposure",
      icon: <Grid3X3 className="w-8 h-8 text-green-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Efficacy Heatmap",
      detailedDescription: "Matrix showing clinical remission rates at Week 12 across all available drugs (rows) and patient populations (bio-naïve vs bio-experienced columns). Green intensity indicates treatment effect size, revealing which drugs work best in treatment-naïve versus refractory patients."
    },
    {
      id: "uc-maintenance-durability",
      label: "UC Maintenance Durability",
      description: "Track remission rates over 52+ weeks",
      icon: <LineChart className="w-8 h-8 text-purple-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Response Timeline",
      detailedDescription: "Tracks clinical remission rates from Week 24 through Week 52+ for the top 5 UC therapies (ozanimod, upadacitinib, mirikizumab, guselkumab, risankizumab). Shows how well response is maintained over time with dotted lines indicating data gaps."
    },
    {
      id: "endoscopic-remission-rankings",
      label: "Endoscopic Remission Rankings",
      description: "Rank UC drugs by mucosal healing",
      icon: <Activity className="w-8 h-8 text-indigo-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Waterfall Plot",
      detailedDescription: "Ranks all UC therapies by endoscopic remission rates at Week 12, displayed from highest to lowest placebo-corrected effect. Useful for identifying which drugs achieve the most robust mucosal healing during induction."
    },
    {
      id: "infection-risk-by-class",
      label: "Infection Risk by Drug Class",
      description: "Compare infection types across drug classes",
      icon: <Grid3X3 className="w-8 h-8 text-red-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Safety Event Matrix",
      detailedDescription: "Comprehensive grid comparing infection-related events (any infection, serious infections, herpes zoster, opportunistic infections) across drug classes (JAK inhibitors, Anti-TNFs, IL-23s, S1P modulators) at Week 52. Red/green coloring shows risk differences versus placebo."
    },
    {
      id: "long-term-safety-comparison",
      label: "Long-term Safety Comparison",
      description: "SAE rates during maintenance therapy",
      icon: <BarChart3 className="w-8 h-8 text-orange-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Horizontal Bar Chart",
      detailedDescription: "Compares SAE rates at Week 52 across all drugs with maintenance data, showing risk differences versus placebo. Bars extend left (lower risk) or right (higher risk) from zero, sorted by magnitude to quickly identify safest long-term options."
    },
    {
      id: "jak-safety-profile",
      label: "JAK Safety Profile",
      description: "JAK-specific events with confidence intervals",
      icon: <TreePine className="w-8 h-8 text-green-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Forest Plot",
      detailedDescription: "Displays risk differences with 95% confidence intervals for known JAK-associated events (herpes zoster, VTE, MACE, serious infections) across all JAK inhibitors at longest available timepoint. Helps assess class-specific safety concerns with statistical precision."
    },
    {
      id: "discontinuation-over-time",
      label: "Discontinuation Over Time",
      description: "Treatment persistence across drug classes",
      icon: <Zap className="w-8 h-8 text-purple-600" />,
      path: "/comparator/compare-drugs",
      chartType: "Cumulative Incidence",
      detailedDescription: "Shows discontinuation rates due to adverse events over time (Week 0-52) for the most commonly used biologics (adalimumab, infliximab, vedolizumab) and newer oral therapies (upadacitinib, ozanimod). Reveals tolerability differences between drug classes over treatment duration."
    }
  ];

  const handleActionClick = (actionId: string) => {
    if (actionId === 'compare-drugs') {
      window.location.href = '/comparator/compare-drugs';
    } else if (actionId === 'congress-data') {
      router.push('/congress');
    } else {
      setSelectedAction(actionId);
      setSelectedVisualization(null);
    }
  };

  const handleVisualizationClick = (visualization: VisualizationThumbnail) => {
    setSelectedVisualization(visualization.id);
    console.log(`Navigating to ${visualization.path} with action: ${selectedAction}`);
  };

  const handleSavedVisualizationClick = (savedViz: SavedVisualization) => {
    setSelectedSavedVisualization(savedViz.id);
    console.log(`Loading saved visualization: ${savedViz.name}`);
  };

  const handleVisualizationPreview = (visualization: VisualizationThumbnail) => {
    setSelectedVisualizationPreview(visualization);
  };

  const getVisualizationUseCase = (vizId: string) => {
    const useCases = {
      'column-chart': 'Ideal for comparing discrete response rates or remission percentages across different treatments. Use this when you want to clearly show which therapy performs best for specific endpoints like clinical response, endoscopic improvement, or histologic healing. The side-by-side comparison makes it easy to rank treatments and identify statistical significance.',
      'ranked-bar-chart': 'Perfect for ranking treatments from most to least effective across multiple efficacy endpoints. This visualization helps identify the best-performing therapy when you need to consider multiple outcomes simultaneously, such as clinical response, endoscopic remission, and quality of life scores.',
      'heatmap': 'Best used when analyzing efficacy patterns across patient subgroups or multiple endpoints simultaneously. This chart excels at revealing which treatments work best for specific patient populations (e.g., by age, disease severity, prior treatment history) and can highlight unexpected patterns in treatment effectiveness.',
      'timeline': 'Essential for understanding how treatment effects change over time during the study period. Use this to compare how quickly different treatments achieve efficacy, whether effects are sustained, and to identify optimal treatment duration. Particularly valuable for comparing induction versus maintenance therapy effectiveness.',
      'safety-matrix': 'Crucial for comparing adverse event profiles across multiple treatments simultaneously. This visualization allows you to quickly identify which treatments have the best overall safety profile and spot concerning patterns in specific adverse events. Essential for benefit-risk assessments.',
      'horizontal-bar': 'Optimal for comparing specific safety endpoints where you want to emphasize the magnitude of differences between treatments. Use this when presenting adverse event rates to stakeholders who need to quickly grasp which treatment has fewer side effects for key safety concerns.',
      'forest-plot': 'The gold standard for presenting risk ratios and confidence intervals from meta-analyses or indirect comparisons. Essential when you need to show statistical significance and uncertainty around safety estimates. Particularly valuable for regulatory submissions and evidence synthesis.',
      'cumulative-incidence': 'Critical for time-to-event safety analysis, showing when adverse events typically occur during treatment. Use this to understand whether safety risks are early (treatment initiation) or late (long-term exposure) and to compare how quickly different treatments reach concerning safety thresholds.'
    };
    return useCases[vizId] || 'This visualization provides unique insights into treatment comparisons that can inform clinical decision-making and strategic planning.';
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Comparator Tool | IBD CI Solution</title>
          <meta name="description" content="Compare and analyze competitive intelligence data" />
        </Head>

        <div className="flex">
          {/* Sidebar */}
          <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 border-r bg-white border-gray-200 flex flex-col`}>
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                {sidebarOpen ? (
                  <div className="flex items-center space-x-3">
                    <img 
                      src="/gd-logo.png" 
                      alt="GlobalData" 
                      className="h-8 w-auto"
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-8">
                    <img 
                      src="/gd-icon.png" 
                      alt="GlobalData" 
                      className="h-8 w-8"
                    />
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="text-gray-600 hover:bg-gray-100"
                >
                  {sidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              <nav className="space-y-2">
                {navigationItems.map((item, index) => (
                  <div key={index}>
                    {!sidebarOpen ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div 
                            className={`flex items-center justify-center px-3 py-2 rounded-md cursor-pointer transition-colors ${
                              item.label === 'Comparator Tool'
                                ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                                : 'text-gray-700 hover:bg-gray-100'
                            }`}
                            onClick={() => item.children && sidebarOpen && toggleSection(index)}
                          >
                            {item.icon}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="ml-2">
                          <p>{item.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <div 
                        className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                          item.label === 'Clinical Trials Comparator'
                            ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => {
                          if (item.label === 'Home') {
                            window.location.href = '/';
                          } else if ('href' in item && item.href && typeof item.href === 'string' && item.href !== '/comparator') {
                            window.location.href = item.href;
                          } else if (item.children && sidebarOpen) {
                            toggleSection(index);
                          }
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          {item.icon}
                          {sidebarOpen && <span className="text-xs font-medium">{item.label}</span>}
                        </div>
                        {item.children && sidebarOpen && (
                          expandedSections[index] ? 
                            <ChevronUp className="w-4 h-4" /> : 
                            <ChevronDown className="w-4 h-4" />
                        )}
                      </div>
                    )}
                    {item.children && sidebarOpen && expandedSections[index] && (
                      <div className="ml-6 mt-2 space-y-1">
                        {item.children.map((child, childIndex) => (
                          <div 
                            key={childIndex} 
                            className={`flex items-center space-x-3 px-3 py-1 rounded-md cursor-pointer hover:bg-gray-50 ${
                              child.label === 'Trial Results Comparator' 
                                ? 'text-blue-700 bg-blue-50 font-medium' 
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                            onClick={() => {
                              if (child.href) {
                                window.location.href = child.href;
                              }
                            }}
                          >
                            {child.icon}
                            <span className="text-xs">{child.label}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>

              {/* Document Library Section */}
              {sidebarOpen && navigationConfig.documentLibrary && (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mx-2">
                    <div className="mb-2">
                      <h3 className="text-xs font-semibold text-blue-800 uppercase tracking-wide">{navigationConfig.documentLibrary.title}</h3>
                    </div>
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-blue-700 hover:text-blue-900 hover:bg-blue-100 transition-colors">
                      {navigationConfig.documentLibrary.link?.icon && getIconComponent(navigationConfig.documentLibrary.link.icon)}
                      <span className="text-xs font-medium">{navigationConfig.documentLibrary.link?.label}</span>
                    </div>
                  </div>

                  {/* Feedback Section */}
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors mx-2">
                      <MessageSquare className="w-4 h-4" />
                      <span className="text-xs font-medium">Help us improve this solution</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 px-8 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Trial Results Comparator</h1>
                    <p className="text-sm text-gray-600 mt-1">Solution prepared by Deallus for Roche</p>
                  </div>
                  <div className="relative">
                    <div 
                      className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full cursor-pointer hover:bg-blue-200 transition-colors"
                      onClick={() => setDiseaseDropdownOpen(!diseaseDropdownOpen)}
                    >
                      <span className="text-sm font-medium text-blue-800">IBD</span>
                      <ChevronDown className={`w-3 h-3 text-blue-600 transition-transform ${diseaseDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>

                    {/* Disease Dropdown */}
                    {diseaseDropdownOpen && (
                      <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        <div className="px-4 py-2 border-b border-gray-100">
                          <div className="text-sm font-semibold text-gray-900">Available Disease Areas</div>
                          <div className="text-xs text-gray-600">Switch between your accessible solutions</div>
                        </div>
                        <div className="py-1">
                          <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between bg-blue-50 border-l-2 border-blue-600">
                            <div>
                              <div className="font-medium text-blue-700">IBD</div>
                              <div className="text-sm text-gray-600">Inflammatory Bowel Disease</div>
                            </div>
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Solution Consultant */}
                <div className="relative">
                  <div 
                    className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors border border-gray-200 bg-white shadow-sm"
                    onClick={() => {
                      setConsultantDropdownOpen(!consultantDropdownOpen);
                      setDiseaseDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      {/* Fake headshot */}
                      <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                        <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500 font-medium">Your Solution Consultant</div>
                        <div className="text-sm font-semibold text-gray-900">James Davidson</div>
                      </div>
                      <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${consultantDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </div>

                  {/* Dropdown */}
                  {consultantDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                      <div className="px-4 py-3 border-b border-gray-100">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                              <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                            </div>
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900">James Davidson</div>
                            <div className="text-sm text-gray-600">Senior Solution Consultant</div>
                          </div>
                        </div>
                      </div>
                      <div className="py-1">
                        <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                          <Mail className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-700"><EMAIL></span>
                        </div>
                        <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-700">+44 20 7936 6400</span>
                        </div>
                        <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                          <Video className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-700">Schedule Teams Call</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="px-8 py-8">
              {/* Main Question */}
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-gray-900 mb-4">
                 Compare Clinial Trial Results
                </h2>
                <p className="text-lg text-gray-600">
                  Choose your starting point to begin your trial results comparison
                </p>
              </div>

              {/* Primary Action Tiles */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                {primaryActions.map((action) => (
                  <Card 
                    key={action.id} 
                    className={`cursor-pointer transition-all duration-200 h-48 flex flex-col ${action.color} ${
                      selectedAction === action.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => handleActionClick(action.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        {action.icon}
                        <CardTitle className="text-lg">{action.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col">
                      <CardDescription className="text-sm mb-3 flex-1">
                        {action.description}
                      </CardDescription>
                      {action.hasSavedOptions && (
                        <div className="relative">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-between text-left"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSavedVisualizationsDropdownOpen(!savedVisualizationsDropdownOpen);
                            }}
                          >
                            <span className="text-xs">
                              {selectedSavedVisualization 
                                ? savedVisualizations.find(v => v.id === selectedSavedVisualization)?.name
                                : "Select Saved Comparators"
                              }
                            </span>
                            <ChevronDown className={`w-3 h-3 transition-transform ${savedVisualizationsDropdownOpen ? 'rotate-180' : ''}`} />
                          </Button>

                          {savedVisualizationsDropdownOpen && (
                            <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                              {savedVisualizations.map((savedViz) => (
                                <div 
                                  key={savedViz.id}
                                  className={`px-3 py-2 cursor-pointer transition-colors hover:bg-gray-50 ${
                                    selectedSavedVisualization === savedViz.id ? 'bg-blue-50 text-blue-700' : ''
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSavedVisualizationClick(savedViz);
                                    setSavedVisualizationsDropdownOpen(false);
                                  }}
                                >
                                  <div>
                                    <div className="text-sm font-medium">{savedViz.name}</div>
                                    <div className="text-xs text-gray-600">{savedViz.type} • {savedViz.description}</div>
                                    {savedViz.sharedBy && (
                                      <div className="text-xs text-blue-600">Shared by {savedViz.sharedBy}</div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Two Column Layout */}
              <div className="border-t pt-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Download & Version History */}
                  <div className="space-y-6">
                    {/* Download Button */}
                    <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-green-800 mb-2">Latest Comparator Data</h3>
                          <p className="text-sm text-green-700">Download the complete Excel dataset with all trial comparisons</p>
                          <p className="text-xs text-green-600 mt-1">Updated: December 2024 • Version 2.3</p>
                        </div>
                        <Button 
                          size="lg"
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => {
                            // Simulate download
                            const link = document.createElement('a');
                            link.href = '#';
                            link.download = 'ibd-trial-comparator-v2.3.xlsx';
                            link.click();
                          }}
                        >
                          <Download className="w-5 h-5 mr-2" />
                          Download Excel
                        </Button>
                      </div>
                    </Card>

                    {/* Version History */}
                    <Card className="p-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-4">Version History</h3>
                      <div className="space-y-4">
                        {versionHistory.map((version, index) => (
                          <div key={version.id} className="flex items-start space-x-4">
                            <div className="flex-shrink-0 relative">
                              <div className="w-3 h-3 bg-blue-600 rounded-full border-2 border-white shadow"></div>
                              {index < versionHistory.length - 1 && (
                                <div className="absolute left-1.5 top-3 w-0.5 h-12 bg-gray-200"></div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium text-gray-900">{version.name}</h4>
                                <span className="text-xs text-gray-500">{version.date}</span>
                              </div>
                              <p className="text-xs text-gray-600 mt-1">{version.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  </div>

                  {/* Right Column - Pre-configured Analyses */}
                  <Card className="p-6">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                      Pre-configured Comparators
                    </h2>

                    {/* Side by side Efficacy and Safety Sections */}
                    <div className="grid grid-cols-2 gap-6">
                      {/* Efficacy Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-blue-700 mb-4 flex items-center">
                          <Target className="w-5 h-5 mr-2" />
                          Efficacy
                        </h3>
                        <div className="space-y-3">
                          {preConfiguredAnalyses.slice(0, 4).map((analysis) => (
                            <Card 
                              key={analysis.id}
                              className="cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50"
                              onClick={() => handleVisualizationPreview(analysis)}
                            >
                              <CardContent className="px-3 py-2">
                                <div className="flex items-start space-x-3">
                                  <div className="flex-shrink-0 mt-1">
                                    {analysis.icon}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-medium text-sm text-gray-900 leading-tight mb-1">{analysis.label}</h4>
                                    <p className="text-xs text-gray-600 leading-tight">{analysis.description}</p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>

                      {/* Safety Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-red-700 mb-4 flex items-center">
                          <Shield className="w-5 h-5 mr-2" />
                          Safety
                        </h3>
                        <div className="space-y-3">
                          {preConfiguredAnalyses.slice(4, 8).map((analysis) => (
                            <Card 
                              key={analysis.id}
                              className="cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50"
                              onClick={() => handleVisualizationPreview(analysis)}
                            >
                              <CardContent className="px-3 py-2">
                                <div className="flex items-start space-x-3">
                                  <div className="flex-shrink-0 mt-1">
                                    {analysis.icon}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-medium text-sm text-gray-900 leading-tight mb-1">{analysis.label}</h4>
                                    <p className="text-xs text-gray-600 leading-tight">{analysis.description}</p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Selected State Display */}
                {selectedAction && selectedVisualization && (
                  <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Selected:</strong> {primaryActions.find(a => a.id === selectedAction)?.title} → {
                        preConfiguredAnalyses.find(v => v.id === selectedVisualization)?.label
                      }
                    </p>
                  </div>
                )}

                {/* Saved Visualization Selected */}
                {selectedSavedVisualization && (
                  <div className="mt-8 p-4 bg-indigo-50 border border-indigo-200 rounded-lg">
                    <p className="text-sm text-indigo-800">
                      <strong>Loading saved visualization:</strong> {savedVisualizations.find(v => v.id === selectedSavedVisualization)?.name}
                    </p>
                  </div>
                )}
              </div>

              {/* Analysis Preview Modal */}
              {selectedVisualizationPreview && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
                  <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">{selectedVisualizationPreview.label}</h3>
                          <p className="text-sm text-gray-600 mt-1">{selectedVisualizationPreview.chartType}</p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedVisualizationPreview(null)}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <span className="sr-only">Close</span>
                          ✕
                        </Button>
                      </div>

                      {/* Chart Preview Placeholder with Overlay */}
                      <div className="relative w-full h-80 bg-gray-200 rounded-lg mb-6 overflow-hidden">
                        {/* Greyed out placeholder */}
                        <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-300 flex items-center justify-center">
                          <div className="w-24 h-24 text-gray-400 opacity-50">
                            {selectedVisualizationPreview.icon}
                          </div>
                        </div>

                        {/* Overlay with description */}
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-8">
                          <div className="bg-white rounded-lg p-6 max-w-lg text-center">
                            <h4 className="font-semibold text-gray-900 mb-3">About this analysis</h4>
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {selectedVisualizationPreview.detailedDescription}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex justify-between items-center">
                        <Button
                          variant="outline"
                          onClick={() => setSelectedVisualizationPreview(null)}
                        >
                          Close
                        </Button>
                        <Button
                          onClick={() => {
                            window.location.href = selectedVisualizationPreview.path;
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Try this visualization
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* About Indirect Comparisons Section */}
              <div className="border-t pt-8 mt-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
                  About indirect comparisons
                </h2>

                <div className="max-w-4xl mx-auto">
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                          <Lightbulb className="w-5 h-5 text-amber-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-amber-900 mb-3">Important Limitations</h3>
                        <div className="text-sm text-amber-800 space-y-3">
                          <p>
                            Indirect comparisons allow for evaluating treatments that have not been directly compared in head-to-head trials. 
                            However, these analyses have inherent limitations that should be carefully considered when interpreting results.
                          </p>
                          <p>
                            <strong>Key considerations include:</strong> Population differences across trials, varying study designs and methodologies, 
                            differences in outcome definitions and measurement timepoints, and potential confounding factors that may affect comparability. 
                            Results should be interpreted with caution and clinical judgment is essential.
                          </p>
                          <p>
                            The strength of indirect comparison evidence is generally considered lower than direct head-to-head trial evidence. 
                            These analyses are most reliable when comparing treatments evaluated in similar patient populations using comparable methodologies.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Comparator;