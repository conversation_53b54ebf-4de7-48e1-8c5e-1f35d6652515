
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import authConfig from './config/auth.json'

export function middleware(request: NextRequest) {
  // Always require password on deployed domains (not localhost/dev)
  const hostname = request.nextUrl.hostname
  const isDevelopment = hostname === 'localhost' || hostname.includes('repl.co')
  
  // Skip password protection only in development
  if (isDevelopment && (hostname === 'localhost' || hostname.includes('127.0.0.1'))) {
    return NextResponse.next()
  }

  // Skip password check for the password page itself and API routes
  if (request.nextUrl.pathname === '/password' || request.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next()
  }

  // Check if user has valid session
  const authCookie = request.cookies.get('auth-session')
  const isAuthenticated = authCookie?.value === 'authenticated'

  if (!isAuthenticated) {
    // Redirect to password page with current path
    const passwordUrl = new URL('/password', request.url)
    passwordUrl.searchParams.set('redirect', request.nextUrl.pathname + request.nextUrl.search)
    return NextResponse.redirect(passwordUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - password (password page)
     * - public assets (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|password|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.gif|.*\\.svg|.*\\.ico).*)',
  ],
}
