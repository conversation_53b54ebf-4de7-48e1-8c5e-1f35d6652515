using System.Data;
using System.Text.Json;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Dapper;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Shared.Exceptions;

namespace GDAPI.Shared.Services;

/// <summary>
/// Service for logging errors to both file and database
/// </summary>
public class ErrorLoggingService : IErrorLoggingService
{
    private readonly ILogger<ErrorLoggingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _connectionString;
    private readonly string _logFilePath;
    private readonly SemaphoreSlim _fileSemaphore;

    public ErrorLoggingService(ILogger<ErrorLoggingService> logger, IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        _connectionString = _configuration.GetConnectionString("DefaultConnection") 
            ?? throw new InvalidOperationException("DefaultConnection connection string is not configured");
        
        // Configure log file path
        var logDirectory = _configuration["Logging:ErrorLogDirectory"] ?? "Logs";
        if (!Path.IsPathRooted(logDirectory))
        {
            logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, logDirectory);
        }
        
        Directory.CreateDirectory(logDirectory);
        _logFilePath = Path.Combine(logDirectory, $"errors-{DateTime.UtcNow:yyyy-MM-dd}.log");
        
        _fileSemaphore = new SemaphoreSlim(1, 1);
    }

    public async Task LogExceptionAsync(Exception exception, ErrorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var errorCode = GetErrorCode(exception);
            var message = exception.Message;
            var stackTrace = exception.StackTrace;
            var innerException = exception.InnerException?.ToString();
            
            // Extract additional details from custom exceptions
            object? details = null;
            if (exception is BaseException baseEx)
            {
                details = baseEx.Details;
                context.CorrelationId ??= baseEx.CorrelationId;
                context.Module ??= baseEx.Module;
                context.UserId ??= baseEx.UserId;
                
                // Add context from exception
                foreach (var kvp in baseEx.Context)
                {
                    context.AdditionalData.TryAdd(kvp.Key, kvp.Value);
                }
            }

            // Log to both file and database in parallel
            var fileTask = LogToFileAsync(errorCode, message, stackTrace, innerException, context, details, cancellationToken);
            var dbTask = LogToDatabaseAsync(errorCode, message, stackTrace, innerException, context, details, cancellationToken);

            await Task.WhenAll(fileTask, dbTask);
        }
        catch (Exception ex)
        {
            // If error logging fails, log to the standard logger as fallback
            _logger.LogCritical(ex, "Failed to log exception: {OriginalException}", exception.Message);
        }
    }

    public async Task LogErrorAsync(string errorCode, string message, ErrorContext context, string severity = "Error", CancellationToken cancellationToken = default)
    {
        try
        {
            // Log to both file and database in parallel
            var fileTask = LogToFileAsync(errorCode, message, null, null, context, null, cancellationToken, severity);
            var dbTask = LogToDatabaseAsync(errorCode, message, null, null, context, null, cancellationToken, severity);

            await Task.WhenAll(fileTask, dbTask);
        }
        catch (Exception ex)
        {
            // If error logging fails, log to the standard logger as fallback
            _logger.LogCritical(ex, "Failed to log error: {ErrorCode} - {Message}", errorCode, message);
        }
    }

    public async Task<(IEnumerable<ErrorLogEntry> Items, int TotalCount)> GetErrorLogsAsync(
        int pageNumber = 1,
        int pageSize = 50,
        string? module = null,
        string? severity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? correlationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            
            var parameters = new
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                Module = module,
                Severity = severity,
                FromDate = fromDate,
                ToDate = toDate,
                CorrelationId = correlationId
            };

            var results = await connection.QueryAsync<ErrorLogEntryWithCount>(
                "GetErrorLogs",
                parameters,
                commandType: CommandType.StoredProcedure);

            var items = results.Select(r => new ErrorLogEntry
            {
                Id = r.Id,
                ErrorCode = r.ErrorCode,
                Message = r.Message,
                StackTrace = r.StackTrace,
                InnerException = r.InnerException,
                CorrelationId = r.CorrelationId,
                Module = r.Module,
                UserId = r.UserId,
                RequestPath = r.RequestPath,
                HttpMethod = r.HttpMethod,
                UserAgent = r.UserAgent,
                IpAddress = r.IpAddress,
                Details = r.Details,
                Context = r.Context,
                CreatedAt = r.CreatedAt,
                Severity = r.Severity,
                Environment = r.Environment,
                MachineName = r.MachineName,
                ProcessId = r.ProcessId,
                ThreadId = r.ThreadId
            });

            var totalCount = results.FirstOrDefault()?.TotalCount ?? 0;

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve error logs");
            return (Enumerable.Empty<ErrorLogEntry>(), 0);
        }
    }

    public async Task<int> CleanupOldLogsAsync(int retentionDays = 90, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            
            var result = await connection.QuerySingleAsync<CleanupResult>(
                "CleanupErrorLogs",
                new { RetentionDays = retentionDays },
                commandType: CommandType.StoredProcedure);

            _logger.LogInformation("Cleaned up {DeletedCount} error log entries older than {RetentionDays} days", 
                result.DeletedCount, retentionDays);

            return result.DeletedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old error logs");
            return 0;
        }
    }

    private async Task LogToFileAsync(string errorCode, string message, string? stackTrace, string? innerException, 
        ErrorContext context, object? details, CancellationToken cancellationToken, string severity = "Error")
    {
        try
        {
            var logEntry = new
            {
                Timestamp = DateTime.UtcNow,
                Severity = severity,
                ErrorCode = errorCode,
                Message = message,
                StackTrace = stackTrace,
                InnerException = innerException,
                CorrelationId = context.CorrelationId,
                Module = context.Module,
                UserId = context.UserId,
                RequestPath = context.RequestPath,
                HttpMethod = context.HttpMethod,
                UserAgent = context.UserAgent,
                IpAddress = context.IpAddress,
                Details = details,
                Context = context.AdditionalData,
                Environment = context.Environment,
                MachineName = context.MachineName,
                ProcessId = context.ProcessId,
                ThreadId = context.ThreadId
            };

            var logLine = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await _fileSemaphore.WaitAsync(cancellationToken);
            try
            {
                await File.AppendAllTextAsync(_logFilePath, logLine + Environment.NewLine, cancellationToken);
            }
            finally
            {
                _fileSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log to file");
        }
    }

    private async Task LogToDatabaseAsync(string errorCode, string message, string? stackTrace, string? innerException, 
        ErrorContext context, object? details, CancellationToken cancellationToken, string severity = "Error")
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            
            var parameters = new
            {
                ErrorCode = errorCode,
                Message = message,
                StackTrace = stackTrace,
                InnerException = innerException,
                CorrelationId = context.CorrelationId,
                Module = context.Module,
                UserId = context.UserId,
                RequestPath = context.RequestPath,
                HttpMethod = context.HttpMethod,
                UserAgent = context.UserAgent,
                IpAddress = context.IpAddress,
                Details = details != null ? JsonSerializer.Serialize(details) : null,
                Context = context.AdditionalData.Any() ? JsonSerializer.Serialize(context.AdditionalData) : null,
                Severity = severity,
                Environment = context.Environment,
                MachineName = context.MachineName,
                ProcessId = context.ProcessId,
                ThreadId = context.ThreadId
            };

            await connection.QuerySingleAsync<int>(
                "InsertErrorLog",
                parameters,
                commandType: CommandType.StoredProcedure);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log to database");
        }
    }

    private static string GetErrorCode(Exception exception)
    {
        return exception switch
        {
            BaseException baseEx => baseEx.ErrorCode,
            ArgumentNullException => "ARGUMENT_NULL",
            ArgumentException => "ARGUMENT_INVALID",
            InvalidOperationException => "INVALID_OPERATION",
            NotSupportedException => "NOT_SUPPORTED",
            TimeoutException => "TIMEOUT",
            UnauthorizedAccessException => "UNAUTHORIZED_ACCESS",
            _ => "UNHANDLED_EXCEPTION"
        };
    }

    private class ErrorLogEntryWithCount : ErrorLogEntry
    {
        public int TotalCount { get; set; }
    }

    private class CleanupResult
    {
        public int DeletedCount { get; set; }
        public DateTime CutoffDate { get; set; }
    }
}
