
import { useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Lock, Eye, EyeOff } from 'lucide-react'

const PasswordPage = () => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      })

      const data = await response.json()

      if (data.success) {
        // Set session cookie with proper settings
        document.cookie = 'auth-session=authenticated; path=/; max-age=86400; SameSite=Lax; Secure=false'
        
        // Use router push instead of window.location for better Next.js handling
        const redirectTo = router.query.redirect as string || '/'
        
        // Ensure we decode any URL encoding
        const decodedRedirect = decodeURIComponent(redirectTo)
        
        // Use replace instead of push to prevent back button issues
        router.replace(decodedRedirect)
      } else {
        setError('Invalid password. Please try again.')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Head>
        <title>Access Required | IBD CI Solution</title>
        <meta name="description" content="Password required to access the application" />
      </Head>

      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Lock className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900">Access Required</CardTitle>
          <p className="text-sm text-gray-600 mt-2">
            Please enter the password to access the Competitive Intelligence Solution
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                required
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={loading}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            
            {error && (
              <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
                {error}
              </div>
            )}

            <Button 
              type="submit" 
              className="w-full" 
              disabled={loading || !password.trim()}
            >
              {loading ? 'Verifying...' : 'Access Application'}
            </Button>
          </form>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              <img 
                src="/gd-logo.png" 
                alt="GlobalData" 
                className="h-6 w-auto"
              />
              <div className="text-xs text-gray-500">
                Competitive Intelligence Solution
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PasswordPage
