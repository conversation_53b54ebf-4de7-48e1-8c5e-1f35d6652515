import type { NextPage } from "next";
import Head from "next/head";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronDown,
  ChevronUp,
  Home as HomeIcon, 
  Map, 
  Calendar, 
  Target, 
  BarChart3, 
  FlaskConical, 
  Scale, 
  Database, 
  Grid3X3, 
  TrendingUp, 
  Lightbulb, 
  Bot, 
  FileText, 
  Building2, 
  Search, 
  Users, 
  MessageSquare, 
  Settings,
  Badge,
  Download,
  Clock,
  Activity,
  PieChart,
  Globe,
  Zap,
  Eye,
  ExternalLink,
  Phone,
  Mail,
  Video
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import solutionConfig from "@/data/solution-config.json";
import navigationConfig from "@/data/navigation-config.json";

// Icon mapping
const iconMap = {
  Calendar: <Calendar className="w-4 h-4" />,
  Target: <Target className="w-4 h-4" />,
  TrendingUp: <TrendingUp className="w-4 h-4" />,
  Map: <Map className="w-4 h-4" />,
  BarChart3: <BarChart3 className="w-4 h-4" />,
  Activity: <Activity className="w-4 h-4" />,
  Users: <Users className="w-4 h-4" />,
  Globe: <Globe className="w-4 h-4" />,
  Bot: <Bot className="w-4 h-4" />,
  MessageSquare: <MessageSquare className="w-4 h-4" />,
  Settings: <Settings className="w-4 h-4" />,
  FileText: <FileText className="w-4 h-4" />,
  Download: <Download className="w-4 h-4" />,
  Lightbulb: <Lightbulb className="w-4 h-4" />,
  Grid3X3: <Grid3X3 className="w-4 h-4" />,
  Search: <Search className="w-4 h-4" />,
  Building2: <Building2 className="w-4 h-4" />,
  FlaskConical: <FlaskConical className="w-4 h-4" />
};

const Home: NextPage = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedSections, setExpandedSections] = useState<{ [key: number]: boolean }>({
    1: true // Start with Development Landscape expanded
  });
  const [consultantDropdownOpen, setConsultantDropdownOpen] = useState(false);
  const [diseaseDropdownOpen, setDiseaseDropdownOpen] = useState(false);
  const [updatesExpanded, setUpdatesExpanded] = useState(false);

  const availableDiseases = [
    { code: "IBD", name: "Inflammatory Bowel Disease", active: true },
    { code: "MASH", name: "Metabolic Dysfunction-Associated Steatohepatitis", active: false },
    { code: "OBESITY", name: "Obesity", active: false }
  ];

  const toggleSection = (index: number) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Helper function to get icon component by name
  const getIconComponent = (iconName: string) => {
    const icons = {
      HomeIcon: <HomeIcon className="w-4 h-4" />,
      Map: <Map className="w-4 h-4" />,
      Calendar: <Calendar className="w-4 h-4" />,
      Target: <Target className="w-4 h-4" />,
      BarChart3: <BarChart3 className="w-4 h-4" />,
      FlaskConical: <FlaskConical className="w-4 h-4" />,
      Scale: <Scale className="w-4 h-4" />,
      Users: <Users className="w-4 h-4" />,
      MessageSquare: <MessageSquare className="w-4 h-4" />,
      Settings: <Settings className="w-4 h-4" />,
      Database: <Database className="w-4 h-4" />,
      Activity: <Activity className="w-4 h-4" />,
      Lightbulb: <Lightbulb className="w-4 h-4" />,
      Grid3X3: <Grid3X3 className="w-4 h-4" />,
      Search: <Search className="w-4 h-4" />,
      TrendingUp: <TrendingUp className="w-4 h-4" />,
      Building2: <Building2 className="w-4 h-4" />,
      Globe: <Globe className="w-4 h-4" />,
      Bot: <Bot className="w-4 h-4" />,
      FileText: <FileText className="w-4 h-4" />
    };
    return icons[iconName as keyof typeof icons] || <HomeIcon className="w-4 h-4" />;
  };

  const navigationItems = navigationConfig.navigationItems.map(item => ({
    ...item,
    icon: getIconComponent(item.icon),
    children: item.children?.map(child => ({
      ...child,
      icon: getIconComponent(child.icon)
    }))
  }));

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Competitive Intelligence Solution | IBD</title>
          <meta name="description" content="Comprehensive competitive intelligence solution for pharmaceutical CI professionals" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 border-r bg-white border-gray-200 flex flex-col`}>
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {sidebarOpen ? (
                <div className="flex items-center space-x-3">
                  <img 
                    src="/gd-logo.png" 
                    alt="GlobalData" 
                    className="h-8 w-auto"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center w-8">
                  <img 
                    src="/gd-icon.png" 
                    alt="GlobalData" 
                    className="h-8 w-8"
                  />
                </div>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="text-gray-600 hover:bg-gray-100"
              >
                {sidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            <nav className="space-y-2">
              {navigationItems.map((item, index) => (
                <div key={index}>
                  {!sidebarOpen ? (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div 
                          className={`flex items-center justify-center px-3 py-2 rounded-md cursor-pointer transition-colors ${
                            item.active 
                              ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                          onClick={() => item.children && sidebarOpen && toggleSection(index)}
                        >
                          {item.icon}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="ml-2">
                        <p>{item.label}</p>
                      </TooltipContent>
                    </Tooltip>
                  ) : (
                    <div 
                        className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                          item.active 
                            ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' 
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={() => {
                          if (item.label === 'Home') {
                            window.location.href = '/';
                          } else if ('href' in item && item.href && typeof item.href === 'string') {
                            window.open(item.href, '_blank');
                          } else if (item.children && sidebarOpen) {
                            toggleSection(index);
                          }
                        }}
                    >
                      <div className="flex items-center space-x-3">
                        {item.icon}
                        {sidebarOpen && <span className="text-xs font-medium">{item.label}</span>}
                      </div>
                      {item.children && sidebarOpen && (
                        expandedSections[index] ? 
                          <ChevronUp className="w-4 h-4" /> : 
                          <ChevronDown className="w-4 h-4" />
                      )}
                    </div>
                  )}
                  {item.children && sidebarOpen && expandedSections[index] && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.children.map((child, childIndex) => (
                          <div 
                            key={childIndex} 
                            className="flex items-center space-x-3 px-3 py-1 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                            onClick={() => {
                              if (child.href) {
                                window.location.href = child.href;
                              }
                            }}
                          >
                            {child.icon}
                            <span className="text-xs">{child.label}</span>
                          </div>
                        ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Document Library Section */}
            {sidebarOpen && navigationConfig.documentLibrary && (
              <div className="border-t border-gray-200 pt-4 mt-4">
                <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mx-2">
                  <div className="mb-2">
                    <h3 className="text-xs font-semibold text-blue-800 uppercase tracking-wide">{navigationConfig.documentLibrary.title}</h3>
                  </div>
                  <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-blue-700 hover:text-blue-900 hover:bg-blue-100 transition-colors">
                    {navigationConfig.documentLibrary.link?.icon && getIconComponent(navigationConfig.documentLibrary.link.icon)}
                    <span className="text-xs font-medium">{navigationConfig.documentLibrary.link?.label}</span>
                  </div>
                </div>

                {/* Feedback Section */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors mx-2">
                    <MessageSquare className="w-4 h-4" />
                    <span className="text-xs font-medium">Help us improve this solution</span>
                  </div>
                </div>
              </div>
            )}


          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Competitive Intelligence Solution</h1>
                  <p className="text-sm text-gray-600 mt-1">Solution prepared by Deallus for Roche</p>
                </div>
                <div className="relative">
                  <div 
                    className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full cursor-pointer hover:bg-blue-200 transition-colors"
                    onClick={() => setDiseaseDropdownOpen(!diseaseDropdownOpen)}
                  >
                    <span className="text-sm font-medium text-blue-800">IBD</span>
                    <ChevronDown className={`w-3 h-3 text-blue-600 transition-transform ${diseaseDropdownOpen ? 'rotate-180' : ''}`} />
                  </div>

                  {/* Disease Dropdown */}
                  {diseaseDropdownOpen && (
                    <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                      <div className="px-4 py-2 border-b border-gray-100">
                        <div className="text-sm font-semibold text-gray-900">Available Disease Areas</div>
                        <div className="text-xs text-gray-600">Switch between your accessible solutions</div>
                      </div>
                      <div className="py-1">
                        {availableDiseases.map((disease, index) => (
                          <div 
                            key={index}
                            className={`px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${
                              disease.active ? 'bg-blue-50 border-l-2 border-blue-600' : ''
                            }`}
                            onClick={() => {
                              // Handle disease switching logic here
                              setDiseaseDropdownOpen(false);
                            }}
                          >
                            <div>
                              <div className={`font-medium ${disease.active ? 'text-blue-700' : 'text-gray-900'}`}>
                                {disease.code}
                              </div>
                              <div className="text-sm text-gray-600">{disease.name}</div>
                            </div>
                            {disease.active && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Solution Consultant */}
              <div className="relative">
                <div 
                  className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors border border-gray-200 bg-white shadow-sm"
                  onClick={() => {
                    setConsultantDropdownOpen(!consultantDropdownOpen);
                    setDiseaseDropdownOpen(false);
                  }}
                >
                  <div className="flex items-center space-x-3">
                    {/* Fake headshot with more realistic styling */}
                    <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                      <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                        {/* Face shape */}
                        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                        {/* Body/shoulders */}
                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                        {/* Professional overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500 font-medium">Your Solution Consultant</div>
                      <div className="text-sm font-semibold text-gray-900">James Davidson</div>
                    </div>
                    <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${consultantDropdownOpen ? 'rotate-180' : ''}`} />
                  </div>
                </div>

                {/* Dropdown */}
                {consultantDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm">
                          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative">
                            {/* Face shape */}
                            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full"></div>
                            {/* Body/shoulders */}
                            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full"></div>
                            {/* Professional overlay */}
                            <div className="absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30"></div>
                          </div>
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">James Davidson</div>
                          <div className="text-sm text-gray-600">Senior Solution Consultant</div>
                        </div>
                      </div>
                    </div>
                    <div className="py-1">
                      <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                        <Mail className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-700"><EMAIL></span>
                      </div>
                      <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-700">+44 20 7936 6400</span>
                      </div>
                      <div className="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3">
                        <Video className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-700">Schedule Teams Call</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Latest Updates Bar */}
          <div className="bg-blue-50 border-b border-blue-200 px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-blue-900">Latest Update:</span>
                <span className="text-sm text-blue-800">Development Bull&apos;s Eye - Enhanced competitor positioning analysis</span>
                <span className="text-xs text-blue-600">2 hours ago</span>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-blue-700 hover:bg-blue-100 text-xs h-6"
                onClick={() => setUpdatesExpanded(!updatesExpanded)}
              >
                {updatesExpanded ? 'Hide Updates' : 'View Latest Updates'}
                <ChevronDown className={`w-3 h-3 ml-1 transition-transform ${updatesExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </div>

            {/* Expanded Updates */}
            {updatesExpanded && (
              <div className="mt-4 border-t border-blue-200 pt-4">
                <div className="space-y-2">
                  {/* Update Items */}
                  <div className="bg-white rounded-lg border border-blue-100 divide-y divide-gray-100">
                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Development Bull&apos;s Eye</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Development Landscape</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Enhanced competitor positioning analysis with new risk assessment metrics</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">15 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Trial Results Comparator</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Clinical Trials</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Added new efficacy benchmarking tools and statistical confidence intervals</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">14 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Research Topic Map</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">KOL Insights</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Improved network visualization with new influence scoring methodology</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">12 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">DDW 2025 Analysis</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Congress Intelligence</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Updated post-congress insights with AI-powered presentation analysis</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">10 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Timeline & Milestones</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Development Landscape</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Added Q2 2025 regulatory submission predictions with confidence scoring</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">08 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Patient Populations</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Clinical Trials</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Enhanced inclusion/exclusion criteria analysis with population overlap metrics</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">07 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Visualization Workspace</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">GlobalData IBD</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">New interactive filtering options and enhanced bull&apos;s eye chart customization</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">01 Jan 2025</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">AI Conversation</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">KOL Insights</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Improved natural language processing for KOL quote analysis and search</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">30 Dec 2024</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">Market Entry Positioning</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Development Landscape</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Updated competitive positioning maps with line-of-therapy analysis</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">25 Dec 2024</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 flex-wrap">
                          <span className="text-xs font-bold text-blue-700">ECCO 2025 Analysis</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-900">Congress Intelligence</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-600">Complete post-congress analysis with competitive insight summaries</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                        <span className="text-xs text-gray-500">15 Dec 2024</span>
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>
                  </div>

                  {/* View Full History Link */}
                  <div className="flex items-center justify-between pt-3 border-t border-blue-100">
                    <div className="text-xs text-gray-500 italic">
                      * Updates exclude GlobalData IBD Data which is refreshed daily
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-blue-700 hover:bg-blue-100 text-xs h-6 px-2"
                    >
                      View Full Version History
                      <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Solution Components */}
          <div className="px-8 py-4 space-y-6">
            {solutionConfig.solutionComponents.map((component, index) => (
              <div key={component.id} className="space-y-4">
                {/* Component Layout - Fixed Left Column, Flexible Right Grid */}
                <div className="flex gap-6 items-start">
                  {/* Left Column - Fixed Width Component Info */}
                  <div className="w-80 flex-shrink-0 space-y-3">
                    <div className="space-y-2">
                      <h2 className="text-lg font-bold text-gray-900">{component.title}</h2>
                      <p className="text-sm text-gray-600 leading-relaxed">{component.description}</p>
                    </div>

                    {/* Quick Links Section */}
                    {component.display?.showQuickLinks && component.quickLinks && (
                      <div className="space-y-2">
                        <div className="text-xs text-gray-500 uppercase tracking-wide font-medium">Quick Links</div>
                        <div className="space-y-1">
                          {component.quickLinks.map((link, linkIndex) => (
                            <Button 
                              key={linkIndex}variant="ghost" 
                              size="sm" 
                              className={`w-full justify-start text-xs h-7 ${
                                link.variant === 'primary' 
                                  ? 'text-blue-600 hover:bg-blue-50' 
                                  : 'text-gray-600 hover:bg-gray-50'
                              }`}
                            >
                              {iconMap[link.icon as keyof typeof iconMap]}
                              <span className="ml-2">{link.label}</span>
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Coverage Section */}
                    {component.display?.showCoverage && component.coverage && (
                      <div className="space-y-2">
                        <div className="text-xs text-gray-500 uppercase tracking-wide font-medium">Coverage</div>
                        <div className="grid grid-cols-2 gap-2">
                          {component.coverage.map((stat, statIndex) => (
                            <div key={statIndex} className="bg-white border border-gray-200 rounded-md p-2 hover:border-gray-300 transition-colors">
                              <div className="flex items-center space-x-1.5">
                                <div className="flex-shrink-0">
                                  {iconMap[stat.icon as keyof typeof iconMap]}
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-sm font-bold text-gray-900">{stat.count.toLocaleString()}</span>
                                  <span className="text-xs text-gray-600">{stat.label}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Right Column - Flexible Visualization Cards Grid */}
                  <div className="flex-1 min-w-0">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                      {component.subComponents.map((subComp, subIndex) => {
                        // Check if this is the Patient Populations component (in Clinical Trials section)
                        const isPatientPopulations = component.id === "clinical-trials" && subComp.title === "Patient Populations";

                        return (
                          <div key={subIndex} className={`group ${isPatientPopulations ? 'cursor-default' : 'cursor-pointer'}`}>
                            <Card className={`border transition-all duration-200 bg-white ${
                              isPatientPopulations 
                                ? 'border-gray-200 bg-gray-50/30' 
                                : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                            }`}>
                              {/* Chart Area */}
                              <div className="h-28 bg-gray-50 relative flex items-center justify-center">
                                <div className={`absolute inset-3 ${subComp.placeholder} backdrop-blur-sm rounded border border-white/20 flex flex-col items-center justify-center p-2 ${
                                  isPatientPopulations ? 'opacity-60' : ''
                                }`}>
                                  <div className="w-6 h-6 bg-white/20 rounded flex items-center justify-center mb-1">
                                    <div className="text-white">
                                      {iconMap[subComp.icon as keyof typeof iconMap]}
                                    </div>
                                  </div>
                                  <div className="text-center text-white">
                                    <div className="text-xs font-medium leading-tight">{subComp.chartType}</div>
                                  </div>
                                </div>

                                {/* Coming Soon Badge */}
                                {isPatientPopulations && (
                                  <div className="absolute top-2 right-2 bg-amber-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                                    Coming Soon
                                  </div>
                                )}
                              </div>

                              {/* Content */}
                              <CardContent className="p-3 space-y-2">
                                <div className="space-y-1">
                                  <h3 className={`font-semibold text-sm leading-tight transition-colors ${
                                    isPatientPopulations 
                                      ? 'text-gray-700' 
                                      : 'text-gray-900 group-hover:text-blue-700'
                                  }`}>
                                    {subComp.title}
                                  </h3>
                                  <p className={`text-xs leading-relaxed line-clamp-2 ${
                                    isPatientPopulations ? 'text-gray-500' : 'text-gray-600'
                                  }`}>
                                    {subComp.description}
                                  </p>
                                </div>

                                <div className="flex items-center justify-center pt-1">
                                  {isPatientPopulations ? (
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="text-xs h-6 px-2 text-gray-600 hover:bg-gray-50 flex items-center space-x-1 cursor-pointer"
                                    >
                                      <span>Share Feedback</span>
                                      <MessageSquare className="w-3 h-3" />
                                    </Button>
                                  ) : (
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="text-xs h-6 px-2 text-blue-600 hover:bg-blue-50 flex items-center space-x-1"
                                      onClick={() => {
                                        if (component.id === "clinical-trials") {
                                          window.location.href = '/comparator';
                                        }
                                      }}
                                    >
                                      <span>{subComp.button?.label || 'Visualise'}</span>
                                      <ExternalLink className="w-3 h-3" />
                                    </Button>
                                  )}
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Separator */}
                {index < solutionConfig.solutionComponents.length - 1 && (
                  <div className="border-t border-gray-200 pt-4"></div>
                )}
              </div>
            ))}
          </div>
        </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Home;