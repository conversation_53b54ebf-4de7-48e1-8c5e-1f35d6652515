[{"ContainingType": "GDAPI.Web.Controllers.AuthController", "Method": "GetCurrentApiKey", "RelativePath": "api/Auth/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Shared.Authentication.Models.ApiKeyInfo, GDAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthController", "Method": "GenerateApiKey", "RelativePath": "api/Auth/generate-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "GDAPI.Web.Controllers.GenerateApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Web.Controllers.GenerateApiKeyResponse, GDAPI.Web, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthController", "Method": "GetApiKeys", "RelativePath": "api/Auth/keys", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[GDAPI.Shared.Authentication.Models.ApiKeyInfo, GDAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthController", "Method": "RevokeApiKey", "RelativePath": "api/Auth/revoke-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "GDAPI.Web.Controllers.RevokeApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthController", "Method": "ValidateApiKey", "RelativePath": "api/Auth/validate-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "GDAPI.Web.Controllers.ValidateApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Web.Controllers.ApiKeyValidationResponse, GDAPI.Web, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthoringController", "Method": "GetAuthoring", "RelativePath": "api/Authoring", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "statusFilter", "Type": "System.String", "IsRequired": false}, {"Name": "categoryFilter", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>er", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Shared.DTOs.Common.PagedResult`1[[GDAPI.Modules.AuthoringTool.Application.DTOs.AuthoringDto, GDAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], GDAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthoringController", "Method": "GetClinicalComparatorsAsync", "RelativePath": "api/Authoring/clinical-comparators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[GDAPI.Modules.AuthoringTool.Application.DTOs.ClinicalComparatorsGroupByIndicationsDto, GDAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthoringController", "Method": "GetHealth", "RelativePath": "api/Authoring/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "GDAPI.Web.Controllers.AuthoringController", "Method": "GetAllUsers", "RelativePath": "api/Authoring/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[GDAPI.Modules.AuthoringTool.Application.DTOs.UserDetailsDto, GDAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.ClinicalTrialsComparatorController", "Method": "GetClinicalTrialsComparator", "RelativePath": "api/ClinicalTrialsComparator", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "moleculeType", "Type": "System.String", "IsRequired": false}, {"Name": "mechanismOfAction", "Type": "System.String", "IsRequired": false}, {"Name": "assetName", "Type": "System.String", "IsRequired": false}, {"Name": "indication", "Type": "System.String", "IsRequired": false}, {"Name": "phase", "Type": "System.String", "IsRequired": false}, {"Name": "trialName", "Type": "System.String", "IsRequired": false}, {"Name": "umbrellaEndPoints", "Type": "System.String", "IsRequired": false}, {"Name": "endPoints", "Type": "System.String", "IsRequired": false}, {"Name": "eTimePoint", "Type": "System.String", "IsRequired": false}, {"Name": "dataHandling", "Type": "System.String", "IsRequired": false}, {"Name": "eventType", "Type": "System.String", "IsRequired": false}, {"Name": "sTimepoint", "Type": "System.String", "IsRequired": false}, {"Name": "dataType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Shared.DTOs.Common.PagedResult`1[[GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs.ClinicalTrialComparatorDto, GDAPI.Modules.ClinicalTrialsComparator, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], GDAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.ClinicalTrialsComparatorController", "Method": "GetFilters", "RelativePath": "api/ClinicalTrialsComparator/filters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs.ClinicalTrialComparatorFiltersDto, GDAPI.Modules.ClinicalTrialsComparator, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.ClinicalTrialsComparatorController", "Method": "GetHealth", "RelativePath": "api/ClinicalTrialsComparator/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "GDAPI.Web.Controllers.ClinicalTrialsComparatorController", "Method": "GetStats", "RelativePath": "api/ClinicalTrialsComparator/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs.ClinicalTrialComparatorStatsDto, GDAPI.Modules.ClinicalTrialsComparator, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigators", "RelativePath": "api/Investigators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "regionFilter", "Type": "System.String", "IsRequired": false}, {"Name": "countryF<PERSON>er", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "GDAPI.Shared.DTOs.Common.ApiResponse`1[[GDAPI.Shared.DTOs.Common.PagedResult`1[[GDAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, GDAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], GDAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "GDAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "GDAPI.Web.Controllers.InvestigatorsController", "Method": "GetHealth", "RelativePath": "api/Investigators/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType1`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Health"], "EndpointName": "HealthCheck"}]