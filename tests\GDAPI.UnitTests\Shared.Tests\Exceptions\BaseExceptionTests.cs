using Xunit;
using FluentAssertions;
using System.Net;
using GDAPI.Shared.Exceptions;

namespace GDAPI.UnitTests.Shared.Tests.Exceptions;

public class BaseExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_SetsMessageCorrectly()
    {
        // Arrange
        var message = "Test exception message";

        // Act
        var exception = new TestBaseException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().NotBeNullOrEmpty();
        exception.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        exception.Module.Should().Be("Unknown");
        exception.Details.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithMessageAndInnerException_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Test exception message";
        var innerException = new InvalidOperationException("Inner exception");

        // Act
        var exception = new TestBaseException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.CorrelationId.Should().NotBeNullOrEmpty();
        exception.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Constructor_WithMessageAndCorrelationId_SetsCorrelationIdCorrectly()
    {
        // Arrange
        var message = "Test exception message";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new TestBaseException(message, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Constructor_WithAllParameters_SetsAllPropertiesCorrectly()
    {
        // Arrange
        var message = "Test exception message";
        var innerException = new InvalidOperationException("Inner exception");
        var correlationId = "test-correlation-id";

        // Act
        var exception = new TestBaseException(message, innerException, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.CorrelationId.Should().Be(correlationId);
        exception.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SetModule_SetsModuleCorrectly()
    {
        // Arrange
        var exception = new TestBaseException("Test message");
        var moduleName = "TestModule";

        // Act
        exception.SetModule(moduleName);

        // Assert
        exception.Module.Should().Be(moduleName);
    }

    [Fact]
    public void SetDetails_SetsDetailsCorrectly()
    {
        // Arrange
        var exception = new TestBaseException("Test message");
        var details = new Dictionary<string, object>
        {
            { "key1", "value1" },
            { "key2", 123 }
        };

        // Act
        exception.SetDetails(details);

        // Assert
        exception.Details.Should().BeEquivalentTo(details);
    }

    [Fact]
    public void AddDetail_AddsDetailCorrectly()
    {
        // Arrange
        var exception = new TestBaseException("Test message");

        // Act
        exception.AddDetail("testKey", "testValue");

        // Assert
        exception.Details.Should().ContainKey("testKey");
        exception.Details!["testKey"].Should().Be("testValue");
    }

    [Fact]
    public void AddDetail_WithExistingDetails_AddsToExistingDictionary()
    {
        // Arrange
        var exception = new TestBaseException("Test message");
        exception.AddDetail("key1", "value1");

        // Act
        exception.AddDetail("key2", "value2");

        // Assert
        exception.Details.Should().HaveCount(2);
        exception.Details!["key1"].Should().Be("value1");
        exception.Details["key2"].Should().Be("value2");
    }

    [Fact]
    public void ToString_ReturnsFormattedString()
    {
        // Arrange
        var message = "Test exception message";
        var correlationId = "test-correlation-id";
        var exception = new TestBaseException(message, correlationId);
        exception.SetModule("TestModule");

        // Act
        var result = exception.ToString();

        // Assert
        result.Should().Contain(message);
        result.Should().Contain(correlationId);
        result.Should().Contain("TestModule");
        result.Should().Contain("TEST_ERROR");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void SetModule_WithInvalidModule_ThrowsArgumentException(string invalidModule)
    {
        // Arrange
        var exception = new TestBaseException("Test message");

        // Act & Assert
        var act = () => exception.SetModule(invalidModule);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void AddDetail_WithNullKey_ThrowsArgumentException()
    {
        // Arrange
        var exception = new TestBaseException("Test message");

        // Act & Assert
        var act = () => exception.AddDetail(null!, "value");
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void CorrelationId_IsGuid_WhenNotProvided()
    {
        // Arrange & Act
        var exception = new TestBaseException("Test message");

        // Assert
        Guid.TryParse(exception.CorrelationId, out _).Should().BeTrue();
    }

    // Test implementation of BaseException for testing purposes
    private class TestBaseException : BaseException
    {
        public override HttpStatusCode StatusCode => HttpStatusCode.InternalServerError;
        public override string ErrorCode => "TEST_ERROR";

        public TestBaseException(string message) : base(message) { }
        public TestBaseException(string message, Exception innerException) : base(message, innerException) { }
        public TestBaseException(string message, string correlationId) : base(message, correlationId) { }
        public TestBaseException(string message, Exception innerException, string correlationId) 
            : base(message, innerException, correlationId) { }
    }
}
