using Xunit;
using FluentAssertions;
using System.Net;
using GDAPI.Shared.Exceptions;

namespace GDAPI.UnitTests.Shared.Tests.Exceptions;

public class ValidationExceptionTests
{
    [Fact]
    public void Constructor_WithMessage_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Validation failed";

        // Act
        var exception = new ValidationException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("VALIDATION_ERROR");
        exception.ValidationErrors.Should().NotBeNull();
        exception.ValidationErrors.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_WithMessageAndValidationErrors_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Validation failed";
        var validationErrors = new Dictionary<string, string[]>
        {
            { "Name", new[] { "Name is required", "Name must be at least 2 characters" } },
            { "Email", new[] { "Email is required", "Email format is invalid" } }
        };

        // Act
        var exception = new ValidationException(message, validationErrors);

        // Assert
        exception.Message.Should().Be(message);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("VALIDATION_ERROR");
        exception.ValidationErrors.Should().BeEquivalentTo(validationErrors);
        exception.Details.Should().BeEquivalentTo(validationErrors);
    }

    [Fact]
    public void Constructor_WithMessageAndCorrelationId_SetsPropertiesCorrectly()
    {
        // Arrange
        var message = "Validation failed";
        var correlationId = "test-correlation-id";

        // Act
        var exception = new ValidationException(message, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        exception.ErrorCode.Should().Be("VALIDATION_ERROR");
    }

    [Fact]
    public void Constructor_WithMessageValidationErrorsAndCorrelationId_SetsAllPropertiesCorrectly()
    {
        // Arrange
        var message = "Validation failed";
        var correlationId = "test-correlation-id";
        var validationErrors = new Dictionary<string, string[]>
        {
            { "Name", new[] { "Name is required" } }
        };

        // Act
        var exception = new ValidationException(message, validationErrors, correlationId);

        // Assert
        exception.Message.Should().Be(message);
        exception.CorrelationId.Should().Be(correlationId);
        exception.ValidationErrors.Should().BeEquivalentTo(validationErrors);
        exception.Details.Should().BeEquivalentTo(validationErrors);
    }

    [Fact]
    public void AddValidationError_WithSingleError_AddsErrorCorrectly()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");
        var field = "Name";
        var error = "Name is required";

        // Act
        exception.AddValidationError(field, error);

        // Assert
        exception.ValidationErrors.Should().ContainKey(field);
        exception.ValidationErrors[field].Should().Contain(error);
        exception.ValidationErrors[field].Should().HaveCount(1);
    }

    [Fact]
    public void AddValidationError_WithMultipleErrorsForSameField_AddsAllErrors()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");
        var field = "Name";
        var error1 = "Name is required";
        var error2 = "Name must be at least 2 characters";

        // Act
        exception.AddValidationError(field, error1);
        exception.AddValidationError(field, error2);

        // Assert
        exception.ValidationErrors.Should().ContainKey(field);
        exception.ValidationErrors[field].Should().Contain(error1);
        exception.ValidationErrors[field].Should().Contain(error2);
        exception.ValidationErrors[field].Should().HaveCount(2);
    }

    [Fact]
    public void AddValidationError_WithMultipleFields_AddsAllFieldsCorrectly()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");

        // Act
        exception.AddValidationError("Name", "Name is required");
        exception.AddValidationError("Email", "Email is required");
        exception.AddValidationError("Age", "Age must be positive");

        // Assert
        exception.ValidationErrors.Should().HaveCount(3);
        exception.ValidationErrors.Should().ContainKey("Name");
        exception.ValidationErrors.Should().ContainKey("Email");
        exception.ValidationErrors.Should().ContainKey("Age");
    }

    [Fact]
    public void HasValidationErrors_WithErrors_ReturnsTrue()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");
        exception.AddValidationError("Name", "Name is required");

        // Act
        var result = exception.HasValidationErrors;

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasValidationErrors_WithoutErrors_ReturnsFalse()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");

        // Act
        var result = exception.HasValidationErrors;

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetValidationErrors_WithErrors_ReturnsAllErrors()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");
        exception.AddValidationError("Name", "Name is required");
        exception.AddValidationError("Email", "Email is invalid");

        // Act
        var result = exception.GetValidationErrors();

        // Assert
        result.Should().HaveCount(2);
        result.Should().ContainKey("Name");
        result.Should().ContainKey("Email");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void AddValidationError_WithInvalidField_ThrowsArgumentException(string invalidField)
    {
        // Arrange
        var exception = new ValidationException("Validation failed");

        // Act & Assert
        var act = () => exception.AddValidationError(invalidField, "Error message");
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void AddValidationError_WithInvalidError_ThrowsArgumentException(string invalidError)
    {
        // Arrange
        var exception = new ValidationException("Validation failed");

        // Act & Assert
        var act = () => exception.AddValidationError("Field", invalidError);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ToString_WithValidationErrors_IncludesValidationErrorsInOutput()
    {
        // Arrange
        var exception = new ValidationException("Validation failed");
        exception.AddValidationError("Name", "Name is required");
        exception.AddValidationError("Email", "Email is invalid");

        // Act
        var result = exception.ToString();

        // Assert
        result.Should().Contain("Validation failed");
        result.Should().Contain("VALIDATION_ERROR");
        result.Should().Contain("Name");
        result.Should().Contain("Email");
    }
}
