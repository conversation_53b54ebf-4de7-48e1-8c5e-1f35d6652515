import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';

interface CumulativeIncidenceProps {
  data: any[];
  title: string;
  config: {};
}

export const CumulativeIncidence: React.FC<CumulativeIncidenceProps> = ({ data, title }) => {
  const uniqueAssets = [...new Set(data.map(item => item.asset))];
  const uniqueEndpoints = [...new Set(data.map(item => item.event_type).filter(Boolean))];
  const uniqueTimepoints = [...new Set(data.map(item => item.timepoint_week))];

  // Check display requirements
  if (uniqueAssets.length >= 10 || uniqueEndpoints.length !== 1 || uniqueTimepoints.length < 3) {
    return (
      <div className="h-80 bg-gradient-to-br from-purple-50 to-indigo-100 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-300">
        <div className="text-center">
          <TrendingUp className="w-16 h-16 text-purple-400 mx-auto mb-4" />
          <p className="text-purple-600 font-medium text-lg">Cumulative Incidence</p>
          <p className="text-sm text-purple-500 mt-2">Requirements not met for this visualization</p>
          <p className="text-xs text-purple-400 mt-1">Need: &lt;10 drugs, single safety event, ≥3 timepoints</p>
          <p className="text-xs text-purple-400">Current: {uniqueAssets.length} drugs, {uniqueEndpoints.length} events, {uniqueTimepoints.length} timepoints</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-purple-50 to-indigo-100 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-300">
        <div className="text-center">
          <TrendingUp className="w-16 h-16 text-purple-400 mx-auto mb-4" />
          <p className="text-purple-600 font-medium text-lg">Cumulative Incidence</p>
          <p className="text-sm text-purple-500 mt-2">No data available with current filter selection</p>
          <p className="text-xs text-purple-400 mt-1">Charting Requirements: 2-5 drugs recommended, Single safety event type, Multiple timepoints (≥3)</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Cumulative incidence over time</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-300">
          <div className="text-center">
            <TrendingUp className="w-16 h-16 text-purple-400 mx-auto mb-4" />
            <p className="text-purple-600 font-medium text-lg">Cumulative Incidence</p>
            <p className="text-sm text-purple-500 mt-2">Chart implementation pending</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};