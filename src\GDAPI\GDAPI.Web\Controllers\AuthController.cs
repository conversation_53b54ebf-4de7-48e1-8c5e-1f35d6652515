using Microsoft.AspNetCore.Mvc;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Shared.Authentication.Services;
using GDAPI.Shared.Authentication.Models;
using GDAPI.Shared.Authentication.Middleware;

namespace GDAPI.Web.Controllers;

/// <summary>
/// Authentication controller for API key management
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IApiKeyService _apiKeyService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IApiKeyService apiKeyService, ILogger<AuthController> logger)
    {
        _apiKeyService = apiKeyService ?? throw new ArgumentNullException(nameof(apiKeyService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Generate a new API key
    /// </summary>
    /// <param name="request">API key generation request</param>
    /// <returns>Generated API key</returns>
    [HttpPost("generate-key")]
    [ProducesResponseType(typeof(ApiResponse<GenerateApiKeyResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<GenerateApiKeyResponse>>> GenerateApiKey([FromBody] GenerateApiKeyRequest request)
    {
        _logger.LogInformation("Generating API key for client: {ClientName}", request.ClientName);

        var apiKey = await _apiKeyService.GenerateApiKeyAsync(
            request.Name,
            request.ClientName,
            request.RateLimit,
            request.ExpiresAt);

        var response = new GenerateApiKeyResponse
        {
            ApiKey = apiKey,
            Name = request.Name,
            ClientName = request.ClientName,
            RateLimit = request.RateLimit ?? 1000,
            ExpiresAt = request.ExpiresAt,
            CreatedAt = DateTime.UtcNow
        };

        return Ok(ApiResponse<GenerateApiKeyResponse>.SuccessResult(response, "API key generated successfully"));
    }

    /// <summary>
    /// Validate an API key
    /// </summary>
    /// <param name="request">API key validation request</param>
    /// <returns>Validation result</returns>
    [HttpPost("validate-key")]
    [ProducesResponseType(typeof(ApiResponse<ApiKeyValidationResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<ApiKeyValidationResponse>>> ValidateApiKey([FromBody] ValidateApiKeyRequest request)
    {
        var clientIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        var validationResult = await _apiKeyService.ValidateApiKeyAsync(request.ApiKey, clientIpAddress);

        var response = new ApiKeyValidationResponse
        {
            IsValid = validationResult.IsValid,
            ErrorMessage = validationResult.ErrorMessage,
            RateLimitExceeded = validationResult.RateLimitExceeded,
            RemainingRequests = validationResult.RemainingRequests,
            RateLimitResetTime = validationResult.RateLimitResetTime,
            ApiKeyInfo = validationResult.ApiKeyInfo
        };

        return Ok(ApiResponse<ApiKeyValidationResponse>.SuccessResult(response, "API key validation completed"));
    }

    /// <summary>
    /// Revoke an API key
    /// </summary>
    /// <param name="request">API key revocation request</param>
    /// <returns>Revocation result</returns>
    [HttpPost("revoke-key")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<bool>>> RevokeApiKey([FromBody] RevokeApiKeyRequest request)
    {
        _logger.LogInformation("Revoking API key");

        var result = await _apiKeyService.RevokeApiKeyAsync(request.ApiKey);

        if (result)
        {
            return Ok(ApiResponse<bool>.SuccessResult(true, "API key revoked successfully"));
        }

        return BadRequest(ApiResponse<bool>.ErrorResult("API key not found or already revoked"));
    }

    /// <summary>
    /// Get all API keys (for management purposes)
    /// </summary>
    /// <returns>List of API keys</returns>
    [HttpGet("keys")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<ApiKeyInfo>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<IEnumerable<ApiKeyInfo>>>> GetApiKeys()
    {
        var apiKeys = await _apiKeyService.GetAllApiKeysAsync();
        
        // Remove sensitive information for display
        var sanitizedKeys = apiKeys.Select(k => new ApiKeyInfo
        {
            Id = k.Id,
            Name = k.Name,
            ClientName = k.ClientName,
            IsActive = k.IsActive,
            RateLimit = k.RateLimit,
            RateLimitWindowMinutes = k.RateLimitWindowMinutes,
            CreatedAt = k.CreatedAt,
            ExpiresAt = k.ExpiresAt,
            LastUsedAt = k.LastUsedAt,
            Metadata = k.Metadata
        });

        return Ok(ApiResponse<IEnumerable<ApiKeyInfo>>.SuccessResult(sanitizedKeys, "API keys retrieved successfully"));
    }

    /// <summary>
    /// Get current API key information
    /// </summary>
    /// <returns>Current API key information</returns>
    [HttpGet("current")]
    [ProducesResponseType(typeof(ApiResponse<ApiKeyInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status401Unauthorized)]
    public ActionResult<ApiResponse<ApiKeyInfo>> GetCurrentApiKey()
    {
        var apiKeyInfo = HttpContext.GetApiKeyInfo();
        
        if (apiKeyInfo == null)
        {
            return Unauthorized(ApiResponse<ApiKeyInfo>.ErrorResult("No API key found in request"));
        }

        var remainingRequests = HttpContext.GetRemainingRequests();

        // Add remaining requests to metadata for display
        var responseInfo = new ApiKeyInfo
        {
            Id = apiKeyInfo.Id,
            Name = apiKeyInfo.Name,
            ClientName = apiKeyInfo.ClientName,
            IsActive = apiKeyInfo.IsActive,
            RateLimit = apiKeyInfo.RateLimit,
            RateLimitWindowMinutes = apiKeyInfo.RateLimitWindowMinutes,
            CreatedAt = apiKeyInfo.CreatedAt,
            ExpiresAt = apiKeyInfo.ExpiresAt,
            LastUsedAt = apiKeyInfo.LastUsedAt,
            Metadata = new Dictionary<string, object>(apiKeyInfo.Metadata)
            {
                ["RemainingRequests"] = remainingRequests ?? 0
            }
        };

        return Ok(ApiResponse<ApiKeyInfo>.SuccessResult(responseInfo, "Current API key information retrieved"));
    }
}

/// <summary>
/// Request model for generating API keys
/// </summary>
public class GenerateApiKeyRequest
{
    /// <summary>
    /// Name for the API key
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Client or application name
    /// </summary>
    public string ClientName { get; set; } = string.Empty;

    /// <summary>
    /// Rate limit for this key (optional)
    /// </summary>
    public int? RateLimit { get; set; }

    /// <summary>
    /// Expiration date (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Response model for API key generation
/// </summary>
public class GenerateApiKeyResponse
{
    /// <summary>
    /// Generated API key
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Name of the API key
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Client name
    /// </summary>
    public string ClientName { get; set; } = string.Empty;

    /// <summary>
    /// Rate limit
    /// </summary>
    public int RateLimit { get; set; }

    /// <summary>
    /// Expiration date
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Request model for validating API keys
/// </summary>
public class ValidateApiKeyRequest
{
    /// <summary>
    /// API key to validate
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;
}

/// <summary>
/// Response model for API key validation
/// </summary>
public class ApiKeyValidationResponse
{
    /// <summary>
    /// Whether the API key is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Error message if validation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether rate limit was exceeded
    /// </summary>
    public bool RateLimitExceeded { get; set; }

    /// <summary>
    /// Remaining requests in current window
    /// </summary>
    public int RemainingRequests { get; set; }

    /// <summary>
    /// When the rate limit window resets
    /// </summary>
    public DateTime? RateLimitResetTime { get; set; }

    /// <summary>
    /// API key information if valid
    /// </summary>
    public ApiKeyInfo? ApiKeyInfo { get; set; }
}

/// <summary>
/// Request model for revoking API keys
/// </summary>
public class RevokeApiKeyRequest
{
    /// <summary>
    /// API key to revoke
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;
}
