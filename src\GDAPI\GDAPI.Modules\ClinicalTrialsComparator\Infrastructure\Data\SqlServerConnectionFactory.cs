using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace GDAPI.Modules.ClinicalTrialsComparator.Infrastructure.Data;

/// <summary>
/// SQL Server connection factory implementation for ClinicalTrialsComparator module
/// </summary>
public class ClinicalTrialsComparatorSqlServerConnectionFactory : IClinicalTrialsComparatorDbConnectionFactory
{
    private readonly string _connectionString;

    public ClinicalTrialsComparatorSqlServerConnectionFactory(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("Pharma_v2Connection") 
            ?? throw new InvalidOperationException("DefaultConnection connection string is not configured");
    }

    public IDbConnection CreateConnection()
    {
        return new SqlConnection(_connectionString);
    }
}
