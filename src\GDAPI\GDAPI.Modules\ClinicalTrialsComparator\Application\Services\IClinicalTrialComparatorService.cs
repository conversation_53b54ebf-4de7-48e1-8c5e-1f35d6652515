using GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.Modules.ClinicalTrialsComparator.Application.Services;

/// <summary>
/// Clinical Trial Comparator service interface for clinical trial comparison operations
/// </summary>
public interface IClinicalTrialComparatorService
{
    /// <summary>
    /// Get paginated clinical trial comparator data using stored procedure with filtering support
    /// </summary>
    /// <param name="request">Search request with pagination and filter parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of clinical trial comparator data</returns>
    Task<ApiResponse<PagedResult<ClinicalTrialComparatorDto>>> GetClinicalTrialComparatorPagedAsync(
        ClinicalTrialComparatorSearchRequest request, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available filter options for clinical trial comparator data
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Available filter options</returns>
    Task<ApiResponse<ClinicalTrialComparatorFiltersDto>> GetFiltersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get clinical trial comparator statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Clinical trial comparator statistics</returns>
    Task<ApiResponse<ClinicalTrialComparatorStatsDto>> GetStatsAsync(CancellationToken cancellationToken = default);
}
