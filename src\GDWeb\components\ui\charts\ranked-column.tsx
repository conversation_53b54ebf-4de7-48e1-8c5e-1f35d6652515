import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';

interface RankedColumnChartProps {
  data: any[];
  title: string;
  config: {
    sortOrder: 'drug-name' | 'timeline';
  };
}

export const RankedColumnChart: React.FC<RankedColumnChartProps> = ({ data, title, config }) => {
  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg flex items-center justify-center border-2 border-dashed border-green-300">
        <div className="text-center">
          <BarChart3 className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <p className="text-green-600 font-medium text-lg">Ranked Bar Chart</p>
          <p className="text-sm text-green-500 mt-2">Select a single endpoint to display ranking</p>
          <p className="text-xs text-green-400 mt-1">Requirements: Single endpoint</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Ranked column comparison of efficacy data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg flex items-center justify-center border-2 border-dashed border-green-300">
          <div className="text-center">
            <BarChart3 className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <p className="text-green-600 font-medium text-lg">Ranked Bar Chart</p>
            <p className="text-sm text-green-500 mt-2">Charting Requirements: Single endpoint</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};