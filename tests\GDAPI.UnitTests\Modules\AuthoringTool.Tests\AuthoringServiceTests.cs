using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using GDAPI.Modules.AuthoringTool.Application.Services;
using GDAPI.Modules.AuthoringTool.Domain.Interfaces;
using GDAPI.Modules.AuthoringTool.Domain.Entities;
using GDAPI.Modules.AuthoringTool.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.UnitTests.Modules.AuthoringTool.Tests;

public class AuthoringServiceTests
{
    private readonly Mock<IAuthoringRepository> _repositoryMock;
    private readonly Mock<ILogger<AuthoringService>> _loggerMock;
    private readonly AuthoringService _service;

    public AuthoringServiceTests()
    {
        _repositoryMock = new Mock<IAuthoringRepository>();
        _loggerMock = new Mock<ILogger<AuthoringService>>();
        _service = new AuthoringService(_repositoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GetAuthoringSamplePagedAsync_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new AuthoringSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "Document",
            StatusFilter = "Draft",
            CategoryFilter = "Technical",
            AuthorFilter = "John Doe",
            DepartmentFilter = "Engineering"
        };

        var authoringItems = new List<Authoring>
        {
            new Authoring
            {
                Id = 1,
                Title = "Technical Document 1",
                Description = "A comprehensive technical document",
                AuthoringType = "Document",
                Category = "Technical",
                Tags = "technical, documentation",
                Author = "John Doe",
                CoAuthors = "Jane Smith",
                Status = "Draft",
                Version = "1.0",
                Department = "Engineering",
                Priority = "High",
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        var totalCount = 1;

        _repositoryMock
            .Setup(x => x.GetAuthoringSamplePagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.CategoryFilter,
                request.AuthorFilter,
                request.DepartmentFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((authoringItems, totalCount));

        // Act
        var result = await _service.GetAuthoringSamplePagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(1);
        result.Data.TotalCount.Should().Be(1);
        result.Message.Should().Be("Authoring items retrieved successfully");
    }

    [Fact]
    public async Task GetAuthoringSamplePagedAsync_EmptyResult_ReturnsSuccessWithEmptyData()
    {
        // Arrange
        var request = new AuthoringSearchRequest
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "NonExistentDocument"
        };

        var authoringItems = new List<Authoring>();
        var totalCount = 0;

        _repositoryMock
            .Setup(x => x.GetAuthoringSamplePagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.StatusFilter,
                request.CategoryFilter,
                request.AuthorFilter,
                request.DepartmentFilter,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((authoringItems, totalCount));

        // Act
        var result = await _service.GetAuthoringSamplePagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().BeEmpty();
        result.Data.TotalCount.Should().Be(0);
        result.Message.Should().Be("Authoring items retrieved successfully");
    }

    [Fact]
    public async Task GetAuthoringSamplePagedAsync_RepositoryThrowsException_ReturnsErrorResponse()
    {
        // Arrange
        var request = new AuthoringSearchRequest
        {
            PageNumber = 1,
            PageSize = 10
        };

        var exception = new InvalidOperationException("Database connection failed");

        _repositoryMock
            .Setup(x => x.GetAuthoringSamplePagedAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _service.GetAuthoringSamplePagedAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Data.Should().BeNull();
        result.Error.Should().NotBeNull();
        result.Error!.Message.Should().Be("An error occurred while retrieving authoring items");
    }

    [Fact]
    public async Task GetAllUsersAsync_ValidType_ReturnsSuccessResponse()
    {
        // Arrange
        var userType = "Active";
        var userDetails = new List<UserDetails>
        {
            new UserDetails
            {
                ShortCode = "JD001",
                DisplayName = "John Doe",
                EmailAddress = "<EMAIL>",
                UserRole = "Author",
                JobTitle = "Senior Writer",
                Status = "Active"
            }
        };

        _repositoryMock
            .Setup(x => x.GetAllUsersAsync(userType))
            .ReturnsAsync(userDetails);

        // Act
        var result = await _service.GetAllUsersAsync(userType);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Should().HaveCount(1);
        result.Message.Should().Be("Users retrieved successfully");
    }

    [Fact]
    public async Task GetAuthoringSamplePagedAsync_NullRequest_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _service.GetAuthoringSamplePagedAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullRepository_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new AuthoringService(null!, _loggerMock.Object);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_NullLogger_ThrowsArgumentNullException()
    {
        // Act & Assert
        var act = () => new AuthoringService(_repositoryMock.Object, null!);
        act.Should().Throw<ArgumentNullException>();
    }
}
