using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using CIDashboard.Models;
using CIDashboard.Services;

namespace CIDashboard.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApiService _apiService;

    public HomeController(ILogger<HomeController> logger, ApiService apiService)
    {
        _logger = logger;
        _apiService = apiService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            // Check API health
            var isApiHealthy = await _apiService.CheckHealthAsync();

            // Get sample dashboard data
            var dashboardStats = new DashboardStats
            {
                TotalUsers = 150,
                ActiveUsers = 142,
                InactiveUsers = 8,
                NewUsersToday = 5,
                NewUsersThisWeek = 23,
                NewUsersThisMonth = 87,
                LastUpdated = DateTime.UtcNow
            };

            ViewBag.ApiHealthy = isApiHealthy;
            ViewBag.DashboardStats = dashboardStats;

            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while loading dashboard");
            ViewBag.ApiHealthy = false;
            ViewBag.DashboardStats = new DashboardStats();
            return View();
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
