﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{F8B7E8A1-2C3D-4E5F-6789-ABCDEF123456}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "GDAPI", "GDAPI", "{DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.Web", "src\GDAPI\GDAPI.Web\GDAPI.Web.csproj", "{A620C54B-7D1A-4483-BF97-E02170D80CC2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.Shared", "src\GDAPI\GDAPI.Shared\GDAPI.Shared.csproj", "{C0496D67-F4BD-448C-8475-D0F303996B4C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.Modules.UserManagement", "src\GDAPI\GDAPI.Modules\UserManagement\GDAPI.Modules.UserManagement.csproj", "{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.Modules.AuthoringTool", "src\GDAPI\GDAPI.Modules\AuthoringTool\GDAPI.Modules.AuthoringTool.csproj", "{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.Modules.ClinicalTrialsComparator", "src\GDAPI\GDAPI.Modules\ClinicalTrialsComparator\GDAPI.Modules.ClinicalTrialsComparator.csproj", "{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDDashboard", "src\GDDashboard\GDDashboard.csproj", "{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.UnitTests", "tests\GDAPI.UnitTests\GDAPI.UnitTests.csproj", "{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDAPI.IntegrationTests", "tests\GDAPI.IntegrationTests\GDAPI.IntegrationTests.csproj", "{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}"
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "GDWeb(1)", "src\GDWeb\", "{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_51192"
		Debug.AspNetCompiler.PhysicalPath = "src\GDWeb\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_51192\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_51192"
		Release.AspNetCompiler.PhysicalPath = "src\GDWeb\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_51192\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "51192"
		SlnRelativePath = "src\GDWeb\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|x64.Build.0 = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Debug|x86.Build.0 = Debug|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|x64.ActiveCfg = Release|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|x64.Build.0 = Release|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|x86.ActiveCfg = Release|Any CPU
		{A620C54B-7D1A-4483-BF97-E02170D80CC2}.Release|x86.Build.0 = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|x64.Build.0 = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Debug|x86.Build.0 = Debug|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|x64.ActiveCfg = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|x64.Build.0 = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|x86.ActiveCfg = Release|Any CPU
		{C0496D67-F4BD-448C-8475-D0F303996B4C}.Release|x86.Build.0 = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|x64.Build.0 = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Debug|x86.Build.0 = Debug|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|x64.ActiveCfg = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|x64.Build.0 = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|x86.ActiveCfg = Release|Any CPU
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55}.Release|x86.Build.0 = Release|Any CPU

		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|x64.Build.0 = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Debug|x86.Build.0 = Debug|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|x64.ActiveCfg = Release|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|x64.Build.0 = Release|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|x86.ActiveCfg = Release|Any CPU
		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C}.Release|x86.Build.0 = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|x64.Build.0 = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Debug|x86.Build.0 = Debug|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|x64.ActiveCfg = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|x64.Build.0 = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|x86.ActiveCfg = Release|Any CPU
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E}.Release|x86.Build.0 = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|x64.Build.0 = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Debug|x86.Build.0 = Debug|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|Any CPU.Build.0 = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|x64.ActiveCfg = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|x64.Build.0 = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|x86.ActiveCfg = Release|Any CPU
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}.Release|x86.Build.0 = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|x64.Build.0 = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Debug|x86.Build.0 = Debug|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|x64.ActiveCfg = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|x64.Build.0 = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|x86.ActiveCfg = Release|Any CPU
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}.Release|x86.Build.0 = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|x64.Build.0 = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|x86.Build.0 = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|x64.ActiveCfg = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|x64.Build.0 = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|x86.ActiveCfg = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|x86.Build.0 = Release|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|x64.Build.0 = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Debug|x86.Build.0 = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|Any CPU.Build.0 = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|x64.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|x64.Build.0 = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|x86.ActiveCfg = Debug|Any CPU
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85}.Release|x86.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DD2D8C65-444E-09DB-591A-EA4C17CA4AB3} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{A620C54B-7D1A-4483-BF97-E02170D80CC2} = {DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}
		{C0496D67-F4BD-448C-8475-D0F303996B4C} = {DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}
		{6BD5A2A5-CFEB-49EB-A156-E7E60389BF55} = {DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}

		{8A923C4D-5E2F-4B6A-9C7D-1F4E8B9A0E3C} = {DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}
		{9B034D5E-6F3A-4C7B-8D9E-2F5A6B7C8D9E} = {DD2D8C65-444E-09DB-591A-EA4C17CA4AB3}
		{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E} = {F8B7E8A1-2C3D-4E5F-6789-ABCDEF123456}
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F} = {F8B7E8A1-2C3D-4E5F-6789-ABCDEF123456}
		{FF7E7446-0FEE-01D7-6B04-EFD61ED25E85} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {18F6E032-7F80-4AED-B901-C367F06E2BD9}
	EndGlobalSection
EndGlobal
