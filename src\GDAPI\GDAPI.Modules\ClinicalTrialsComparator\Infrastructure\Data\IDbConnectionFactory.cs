using System.Data;

namespace GDAPI.Modules.ClinicalTrialsComparator.Infrastructure.Data;

/// <summary>
/// Factory interface for creating database connections for ClinicalTrialsComparator module
/// </summary>
public interface IClinicalTrialsComparatorDbConnectionFactory
{
    /// <summary>
    /// Creates a new database connection for ClinicalTrialsComparator module
    /// </summary>
    /// <returns>Database connection instance</returns>
    IDbConnection CreateConnection();
}
