
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Grid3X3 } from 'lucide-react';

interface EfficacyHeatmapChartProps {
  data: any[];
  title: string;
  config: {
    sortOrder: 'drug-name' | 'timeline';
  };
}

export const EfficacyHeatmapChart: React.FC<EfficacyHeatmapChartProps> = ({ data, title, config }) => {
  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg flex items-center justify-center border-2 border-dashed border-orange-300">
        <div className="text-center">
          <Grid3X3 className="w-16 h-16 text-orange-400 mx-auto mb-4" />
          <p className="text-orange-600 font-medium text-lg">Efficacy Heatmap</p>
          <p className="text-sm text-orange-500 mt-2">Select filters to display heatmap data</p>
          <p className="text-xs text-orange-400 mt-1">Requirements: Single endpoint</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Efficacy heatmap visualization</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg flex items-center justify-center border-2 border-dashed border-orange-300">
          <div className="text-center">
            <Grid3X3 className="w-16 h-16 text-orange-400 mx-auto mb-4" />
            <p className="text-orange-600 font-medium text-lg">Efficacy Heatmap</p>
            <p className="text-sm text-orange-500 mt-2">No specific requirements</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
