
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';

interface ResponseTimelineChartProps {
  data: any[];
  title: string;
  config: {
    showDataGaps: boolean;
  };
}

export const ResponseTimelineChart: React.FC<ResponseTimelineChartProps> = ({ data, title, config }) => {
  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-300">
        <div className="text-center">
          <TrendingUp className="w-16 h-16 text-purple-400 mx-auto mb-4" />
          <p className="text-purple-600 font-medium text-lg">Response Timeline Chart</p>
          <p className="text-sm text-purple-500 mt-2">Select a single endpoint with multiple timepoints</p>
          <p className="text-xs text-purple-400 mt-1">Requirements: Single endpoint, multiple timepoints</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Response timeline over multiple timepoints</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-300">
          <div className="text-center">
            <TrendingUp className="w-16 h-16 text-purple-400 mx-auto mb-4" />
            <p className="text-purple-600 font-medium text-lg">Response Timeline Chart</p>
            <p className="text-sm text-purple-500 mt-2">Charting Requirements: Single endpoint, less than 10 assets recommended, multiple timepoints</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
