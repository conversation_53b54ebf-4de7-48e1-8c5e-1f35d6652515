
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';

interface HorizontalBarSafetyProps {
  data: any[];
  title: string;
  config?: {
    sortOrder?: 'magnitude' | 'drug-name';
  };
}

export const HorizontalBarSafety: React.FC<HorizontalBarSafetyProps> = ({ data, title, config }) => {
  const uniqueEndpoints = [...new Set(data.map(item => item.event_type).filter(Boolean))];
  const uniqueTimepoints = [...new Set(data.map(item => item.timepoint_week))];
  
  // Check display requirements
  if (uniqueEndpoints.length !== 1 || uniqueTimepoints.length !== 1) {
    return (
      <div className="h-80 bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg flex items-center justify-center border-2 border-dashed border-orange-300">
        <div className="text-center">
          <BarChart3 className="w-16 h-16 text-orange-400 mx-auto mb-4" />
          <p className="text-orange-600 font-medium text-lg">Ranked Bar Chart</p>
          <p className="text-sm text-orange-500 mt-2">Requires single safety event and single timepoint</p>
          <p className="text-xs text-orange-400 mt-1">Current: {uniqueEndpoints.length} event types, {uniqueTimepoints.length} timepoints</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-80 bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg flex items-center justify-center border-2 border-dashed border-orange-300">
        <div className="text-center">
          <BarChart3 className="w-16 h-16 text-orange-400 mx-auto mb-4" />
          <p className="text-orange-600 font-medium text-lg">Ranked Bar Chart</p>
          <p className="text-sm text-orange-500 mt-2">No data available with current filter selection</p>
          <p className="text-xs text-orange-400 mt-1">Charting Requirements: Single safety event, Single timepoint</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>Ranked bar safety comparison</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg flex items-center justify-center border-2 border-dashed border-orange-300">
          <div className="text-center">
            <BarChart3 className="w-16 h-16 text-orange-400 mx-auto mb-4" />
            <p className="text-orange-600 font-medium text-lg">Ranked Bar Chart</p>
            <p className="text-sm text-orange-500 mt-2">Chart implementation pending</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
