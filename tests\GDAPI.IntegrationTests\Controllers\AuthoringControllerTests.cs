using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Json;
using System.Net;
using Xunit;
using FluentAssertions;
using GDAPI.Modules.AuthoringTool.Application.DTOs;
using GDAPI.Shared.DTOs.Common;

namespace GDAPI.IntegrationTests.Controllers;

public class AuthoringControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public AuthoringControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        
        // Add API key for authentication
        _client.DefaultRequestHeaders.Add("X-API-Key", "test-api-key");
    }

    [Fact]
    public async Task GetAuthoring_ValidId_ReturnsOkWithData()
    {
        // Arrange
        var authoringId = 1;

        // Act
        var response = await _client.GetAsync($"/api/authoring/{authoringId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Id.Should().Be(authoringId);
    }

    [Fact]
    public async Task GetAuthoring_InvalidId_ReturnsBadRequest()
    {
        // Arrange
        var invalidId = -1;

        // Act
        var response = await _client.GetAsync($"/api/authoring/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeFalse();
        content.Error.Should().NotBeNull();
        content.Error!.Message.Should().Contain("ID must be greater than 0");
    }

    [Fact]
    public async Task GetAuthoring_NotFound_ReturnsNotFound()
    {
        // Arrange
        var nonExistentId = 99999;

        // Act
        var response = await _client.GetAsync($"/api/authoring/{nonExistentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeFalse();
        content.Error.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateAuthoring_ValidData_ReturnsCreated()
    {
        // Arrange
        var createDto = new CreateAuthoringDto
        {
            Title = "Integration Test Document",
            Content = "This is a test document created during integration testing",
            CategoryId = 1
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/authoring", createDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Title.Should().Be(createDto.Title);
        content.Data.Content.Should().Be(createDto.Content);
        content.Data.Status.Should().Be("Draft");
        
        // Verify Location header
        response.Headers.Location.Should().NotBeNull();
        response.Headers.Location!.ToString().Should().Contain("/api/authoring/");
    }

    [Fact]
    public async Task CreateAuthoring_InvalidData_ReturnsBadRequest()
    {
        // Arrange
        var invalidDto = new CreateAuthoringDto
        {
            Title = "", // Invalid: empty title
            Content = "Valid content",
            CategoryId = 1
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/authoring", invalidDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeFalse();
        content.Error.Should().NotBeNull();
        content.Error!.Message.Should().Contain("Title is required");
    }

    [Fact]
    public async Task UpdateAuthoring_ValidData_ReturnsOk()
    {
        // Arrange
        var authoringId = 1;
        var updateDto = new UpdateAuthoringDto
        {
            Title = "Updated Integration Test Document",
            Content = "This document has been updated during integration testing"
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/authoring/{authoringId}", updateDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().BeTrue();
    }

    [Fact]
    public async Task DeleteAuthoring_ExistingId_ReturnsOk()
    {
        // Arrange - First create a document to delete
        var createDto = new CreateAuthoringDto
        {
            Title = "Document to Delete",
            Content = "This document will be deleted",
            CategoryId = 1
        };

        var createResponse = await _client.PostAsJsonAsync("/api/authoring", createDto);
        var createContent = await createResponse.Content.ReadFromJsonAsync<ApiResponse<AuthoringDto>>();
        var authoringId = createContent!.Data!.Id;

        // Act
        var response = await _client.DeleteAsync($"/api/authoring/{authoringId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().BeTrue();

        // Verify the document is actually deleted
        var getResponse = await _client.GetAsync($"/api/authoring/{authoringId}");
        getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetAllAuthoring_ReturnsOkWithPaginatedData()
    {
        // Act
        var response = await _client.GetAsync("/api/authoring?page=1&pageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ApiResponse<PaginatedResult<AuthoringDto>>>();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Items.Should().NotBeNull();
        content.Data.TotalCount.Should().BeGreaterOrEqualTo(0);
        content.Data.Page.Should().Be(1);
        content.Data.PageSize.Should().Be(10);
    }

    [Fact]
    public async Task AuthoringController_WithoutApiKey_ReturnsUnauthorized()
    {
        // Arrange
        var clientWithoutApiKey = _factory.CreateClient();

        // Act
        var response = await clientWithoutApiKey.GetAsync("/api/authoring/1");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }
}
