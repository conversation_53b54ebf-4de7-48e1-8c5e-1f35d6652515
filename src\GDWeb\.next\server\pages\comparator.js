/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/comparator";
exports.ids = ["pages/comparator"];
exports.modules = {

/***/ "(pages-dir-node)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/tooltip.tsx":
/*!***********************************!*\
  !*** ./components/ui/tooltip.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"@radix-ui/react-tooltip\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \n\n\n\nfunction TooltipProvider({ delayDuration = 0, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        \"data-slot\": \"tooltip-provider\",\n        delayDuration: delayDuration,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction Tooltip({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Root, {\n            \"data-slot\": \"tooltip\",\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\nfunction TooltipTrigger({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Trigger, {\n        \"data-slot\": \"tooltip-trigger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 34,\n        columnNumber: 10\n    }, this);\n}\nfunction TooltipContent({ className, sideOffset = 0, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Content, {\n            \"data-slot\": \"tooltip-content\",\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\", className),\n            ...props,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Arrow, {\n                    className: \"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/tooltip.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./data/navigation-config.json":
/*!*************************************!*\
  !*** ./data/navigation-config.json ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigationItems":[{"icon":"HomeIcon","label":"Home","active":true},{"icon":"Map","label":"Development Landscape","children":[{"icon":"Calendar","label":"Timelines & Milestones"},{"icon":"Target","label":"Development Bull\'s Eye"},{"icon":"Map","label":"Market Entry Positioning"},{"icon":"Lightbulb","label":"Landscape Insights"}]},{"icon":"FlaskConical","label":"Clinical Trials Comparator","children":[{"icon":"BarChart3","label":"Trial Results Comparator","href":"/comparator"},{"icon":"Grid3X3","label":"Endpoint Analyser"},{"icon":"Users","label":"Patient Populations"}]},{"icon":"Calendar","label":"Congress Intelligence","children":[{"icon":"Calendar","label":"DDW 2025 (Upcoming)"},{"icon":"Search","label":"ECCO 2025"},{"icon":"Search","label":"DDW 2024"}]},{"icon":"Database","label":"GlobalData IBD Data","children":[{"icon":"Search","label":"Drugs & Trials Data Workspace"},{"icon":"Building2","label":"Deals Analysis"}]},{"icon":"Users","label":"KOL Insights","children":[{"icon":"Globe","label":"KOL Research Topic Map"},{"icon":"Bot","label":"AI-powered Conversation"}]},{"icon":"Settings","label":"Configuration"},{"icon":"FileText","label":"Help & About"}],"documentLibrary":{"title":"Your Document Library","link":{"label":"Project SharePoint","icon":"FileText","url":"#"}}}');

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2xpYi91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxBdmluYXNoXFxBdmluYXNoX0NJU29sdXRpb25cXENJU29sdXRpb25cXHNyY1xcR0RXZWJcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcomparator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccomparator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcomparator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccomparator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\comparator.tsx */ \"(pages-dir-node)/./pages/comparator.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/comparator\",\n        pathname: \"/comparator\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_comparator_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcomparator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccomparator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUc5QixTQUFTQSxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQy9DLHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQztBQUVBLGlFQUFlRixLQUFLQSxFQUFBIiwic291cmNlcyI6WyJDOlxcQXZpbmFzaFxcQXZpbmFzaF9DSVNvbHV0aW9uXFxDSVNvbHV0aW9uXFxzcmNcXEdEV2ViXFxwYWdlc1xcX2FwcC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHBcbiJdLCJuYW1lcyI6WyJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/comparator.tsx":
/*!******************************!*\
  !*** ./pages/comparator.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bookmark,Bot,Building2,Calendar,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Database,Download,FileText,FlaskConical,Globe,Grid3X3,Home,Lightbulb,LineChart,Mail,Map,MessageSquare,Phone,Scale,Search,Settings,Shield,Target,TreePine,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Activity,BarChart3,Bookmark,Bot,Building2,Calendar,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Database,Download,FileText,FlaskConical,Globe,Grid3X3,Home,Lightbulb,LineChart,Mail,Map,MessageSquare,Phone,Scale,Search,Settings,Shield,Target,TreePine,TrendingUp,Users,Video,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(pages-dir-node)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/navigation-config.json */ \"(pages-dir-node)/./data/navigation-config.json\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_card__WEBPACK_IMPORTED_MODULE_4__, _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_card__WEBPACK_IMPORTED_MODULE_4__, _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst Comparator = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [selectedAction, setSelectedAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedVisualization, setSelectedVisualization] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        2: true // Start with Clinical Trials Comparator expanded (index 2)\n    });\n    const [selectedSavedVisualization, setSelectedSavedVisualization] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [consultantDropdownOpen, setConsultantDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [diseaseDropdownOpen, setDiseaseDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [savedVisualizationsDropdownOpen, setSavedVisualizationsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedVisualizationPreview, setSelectedVisualizationPreview] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const toggleSection = (index)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [index]: !prev[index]\n            }));\n    };\n    // Helper function to get icon component by name\n    const getIconComponent = (iconName)=>{\n        const icons = {\n            HomeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Home, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 89,\n                columnNumber: 17\n            }, undefined),\n            Map: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Map, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 90,\n                columnNumber: 12\n            }, undefined),\n            Calendar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calendar, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, undefined),\n            Target: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Target, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 92,\n                columnNumber: 15\n            }, undefined),\n            BarChart3: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BarChart3, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 93,\n                columnNumber: 18\n            }, undefined),\n            FlaskConical: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlaskConical, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 94,\n                columnNumber: 21\n            }, undefined),\n            Scale: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Scale, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 95,\n                columnNumber: 14\n            }, undefined),\n            Users: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Users, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 96,\n                columnNumber: 14\n            }, undefined),\n            MessageSquare: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.MessageSquare, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 97,\n                columnNumber: 22\n            }, undefined),\n            Settings: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Settings, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 98,\n                columnNumber: 17\n            }, undefined),\n            Database: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Database, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 99,\n                columnNumber: 17\n            }, undefined),\n            Activity: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Activity, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 100,\n                columnNumber: 17\n            }, undefined),\n            Lightbulb: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Lightbulb, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 101,\n                columnNumber: 18\n            }, undefined),\n            Grid3X3: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Grid3X3, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 102,\n                columnNumber: 16\n            }, undefined),\n            Search: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 103,\n                columnNumber: 15\n            }, undefined),\n            TrendingUp: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TrendingUp, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 104,\n                columnNumber: 19\n            }, undefined),\n            Building2: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Building2, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 105,\n                columnNumber: 18\n            }, undefined),\n            Globe: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Globe, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 106,\n                columnNumber: 14\n            }, undefined),\n            Bot: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Bot, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 107,\n                columnNumber: 12\n            }, undefined),\n            FileText: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 108,\n                columnNumber: 17\n            }, undefined)\n        };\n        return icons[iconName] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Home, {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n            lineNumber: 110,\n            columnNumber: 53\n        }, undefined);\n    };\n    const navigationItems = _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.navigationItems.map((item)=>({\n            ...item,\n            icon: getIconComponent(item.icon),\n            children: item.children?.map((child)=>({\n                    ...child,\n                    icon: getIconComponent(child.icon)\n                }))\n        }));\n    const savedVisualizations = [\n        {\n            id: \"saved-1\",\n            name: \"Q4 2024 Efficacy Review\",\n            type: \"Bar Chart\",\n            description: \"Response rates across key trials\"\n        },\n        {\n            id: \"saved-2\",\n            name: \"Safety Profile Comparison\",\n            type: \"Safety Matrix\",\n            description: \"AE frequencies for top 5 drugs\"\n        },\n        {\n            id: \"saved-3\",\n            name: \"Conference Data Analysis\",\n            type: \"Waterfall Plot\",\n            description: \"ECCO 2024 presentation insights\",\n            sharedBy: \"Sarah Lyon\"\n        }\n    ];\n    const versionHistory = [\n        {\n            id: \"v2.3\",\n            name: \"Version 2.3\",\n            date: \"Dec 15, 2024\",\n            description: \"Added ECCO 2024 congress data and updated safety profiles for 12 new trials.\"\n        },\n        {\n            id: \"v2.2\",\n            name: \"Version 2.2\",\n            date: \"Nov 8, 2024\",\n            description: \"Enhanced efficacy endpoints and included biosimilar comparison data.\"\n        },\n        {\n            id: \"v2.1\",\n            name: \"Version 2.1\",\n            date: \"Oct 22, 2024\",\n            description: \"Major update with 15 new trials and improved data validation processes.\"\n        },\n        {\n            id: \"v2.0\",\n            name: \"Version 2.0\",\n            date: \"Sep 30, 2024\",\n            description: \"Complete platform redesign with new visualization capabilities and expanded dataset.\"\n        }\n    ];\n    const primaryActions = [\n        {\n            id: \"compare-drugs\",\n            title: \"Compare therapy efficacy and safety\",\n            description: \"Side-by-side comparison of treatment efficacy and safety profiles\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Target, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 176,\n                columnNumber: 13\n            }, undefined),\n            color: \"bg-blue-50 border-blue-200 hover:bg-blue-100\"\n        },\n        {\n            id: \"congress-data\",\n            title: \"Review latest congress data\",\n            description: \"Access to the latest presentations and data from scientific congresses and meetings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calendar, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 183,\n                columnNumber: 13\n            }, undefined),\n            color: \"bg-orange-50 border-orange-200 hover:bg-orange-100\"\n        },\n        {\n            id: \"saved-visualizations\",\n            title: \"Your Saved Comparators\",\n            description: \"Quick access to your previously saved chart configurations and analysis setups\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Bookmark, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 190,\n                columnNumber: 13\n            }, undefined),\n            color: \"bg-indigo-50 border-indigo-200 hover:bg-indigo-100\",\n            hasSavedOptions: true\n        }\n    ];\n    const preConfiguredAnalyses = [\n        {\n            id: \"jak-inhibitor-efficacy\",\n            label: \"JAK Inhibitor Efficacy in Crohn's Disease\",\n            description: \"Compare JAK inhibitors' remission rates in CD\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BarChart3, {\n                className: \"w-8 h-8 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 201,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Grouped Bar Chart\",\n            detailedDescription: \"Compares clinical remission rates (CDAI) at Week 12 for all JAK inhibitors (upadacitinib, ritlecitinib, brepocitinib) in CD patients, showing placebo-corrected differences with p-values and error bars. Helps assess class efficacy in induction.\"\n        },\n        {\n            id: \"bio-naive-vs-experienced\",\n            label: \"Bio-Naïve vs Bio-Experienced Response\",\n            description: \"Treatment effects by prior therapy exposure\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Grid3X3, {\n                className: \"w-8 h-8 text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 210,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Efficacy Heatmap\",\n            detailedDescription: \"Matrix showing clinical remission rates at Week 12 across all available drugs (rows) and patient populations (bio-naïve vs bio-experienced columns). Green intensity indicates treatment effect size, revealing which drugs work best in treatment-naïve versus refractory patients.\"\n        },\n        {\n            id: \"uc-maintenance-durability\",\n            label: \"UC Maintenance Durability\",\n            description: \"Track remission rates over 52+ weeks\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.LineChart, {\n                className: \"w-8 h-8 text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 219,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Response Timeline\",\n            detailedDescription: \"Tracks clinical remission rates from Week 24 through Week 52+ for the top 5 UC therapies (ozanimod, upadacitinib, mirikizumab, guselkumab, risankizumab). Shows how well response is maintained over time with dotted lines indicating data gaps.\"\n        },\n        {\n            id: \"endoscopic-remission-rankings\",\n            label: \"Endoscopic Remission Rankings\",\n            description: \"Rank UC drugs by mucosal healing\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Activity, {\n                className: \"w-8 h-8 text-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 228,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Waterfall Plot\",\n            detailedDescription: \"Ranks all UC therapies by endoscopic remission rates at Week 12, displayed from highest to lowest placebo-corrected effect. Useful for identifying which drugs achieve the most robust mucosal healing during induction.\"\n        },\n        {\n            id: \"infection-risk-by-class\",\n            label: \"Infection Risk by Drug Class\",\n            description: \"Compare infection types across drug classes\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Grid3X3, {\n                className: \"w-8 h-8 text-red-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 237,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Safety Event Matrix\",\n            detailedDescription: \"Comprehensive grid comparing infection-related events (any infection, serious infections, herpes zoster, opportunistic infections) across drug classes (JAK inhibitors, Anti-TNFs, IL-23s, S1P modulators) at Week 52. Red/green coloring shows risk differences versus placebo.\"\n        },\n        {\n            id: \"long-term-safety-comparison\",\n            label: \"Long-term Safety Comparison\",\n            description: \"SAE rates during maintenance therapy\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BarChart3, {\n                className: \"w-8 h-8 text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 246,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Horizontal Bar Chart\",\n            detailedDescription: \"Compares SAE rates at Week 52 across all drugs with maintenance data, showing risk differences versus placebo. Bars extend left (lower risk) or right (higher risk) from zero, sorted by magnitude to quickly identify safest long-term options.\"\n        },\n        {\n            id: \"jak-safety-profile\",\n            label: \"JAK Safety Profile\",\n            description: \"JAK-specific events with confidence intervals\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TreePine, {\n                className: \"w-8 h-8 text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Forest Plot\",\n            detailedDescription: \"Displays risk differences with 95% confidence intervals for known JAK-associated events (herpes zoster, VTE, MACE, serious infections) across all JAK inhibitors at longest available timepoint. Helps assess class-specific safety concerns with statistical precision.\"\n        },\n        {\n            id: \"discontinuation-over-time\",\n            label: \"Discontinuation Over Time\",\n            description: \"Treatment persistence across drug classes\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Zap, {\n                className: \"w-8 h-8 text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                lineNumber: 264,\n                columnNumber: 13\n            }, undefined),\n            path: \"/comparator/compare-drugs\",\n            chartType: \"Cumulative Incidence\",\n            detailedDescription: \"Shows discontinuation rates due to adverse events over time (Week 0-52) for the most commonly used biologics (adalimumab, infliximab, vedolizumab) and newer oral therapies (upadacitinib, ozanimod). Reveals tolerability differences between drug classes over treatment duration.\"\n        }\n    ];\n    const handleActionClick = (actionId)=>{\n        if (actionId === 'compare-drugs') {\n            window.location.href = '/comparator/compare-drugs';\n        } else if (actionId === 'congress-data') {\n            router.push('/congress');\n        } else {\n            setSelectedAction(actionId);\n            setSelectedVisualization(null);\n        }\n    };\n    const handleVisualizationClick = (visualization)=>{\n        setSelectedVisualization(visualization.id);\n        console.log(`Navigating to ${visualization.path} with action: ${selectedAction}`);\n    };\n    const handleSavedVisualizationClick = (savedViz)=>{\n        setSelectedSavedVisualization(savedViz.id);\n        console.log(`Loading saved visualization: ${savedViz.name}`);\n    };\n    const handleVisualizationPreview = (visualization)=>{\n        setSelectedVisualizationPreview(visualization);\n    };\n    const getVisualizationUseCase = (vizId)=>{\n        const useCases = {\n            'column-chart': 'Ideal for comparing discrete response rates or remission percentages across different treatments. Use this when you want to clearly show which therapy performs best for specific endpoints like clinical response, endoscopic improvement, or histologic healing. The side-by-side comparison makes it easy to rank treatments and identify statistical significance.',\n            'ranked-bar-chart': 'Perfect for ranking treatments from most to least effective across multiple efficacy endpoints. This visualization helps identify the best-performing therapy when you need to consider multiple outcomes simultaneously, such as clinical response, endoscopic remission, and quality of life scores.',\n            'heatmap': 'Best used when analyzing efficacy patterns across patient subgroups or multiple endpoints simultaneously. This chart excels at revealing which treatments work best for specific patient populations (e.g., by age, disease severity, prior treatment history) and can highlight unexpected patterns in treatment effectiveness.',\n            'timeline': 'Essential for understanding how treatment effects change over time during the study period. Use this to compare how quickly different treatments achieve efficacy, whether effects are sustained, and to identify optimal treatment duration. Particularly valuable for comparing induction versus maintenance therapy effectiveness.',\n            'safety-matrix': 'Crucial for comparing adverse event profiles across multiple treatments simultaneously. This visualization allows you to quickly identify which treatments have the best overall safety profile and spot concerning patterns in specific adverse events. Essential for benefit-risk assessments.',\n            'horizontal-bar': 'Optimal for comparing specific safety endpoints where you want to emphasize the magnitude of differences between treatments. Use this when presenting adverse event rates to stakeholders who need to quickly grasp which treatment has fewer side effects for key safety concerns.',\n            'forest-plot': 'The gold standard for presenting risk ratios and confidence intervals from meta-analyses or indirect comparisons. Essential when you need to show statistical significance and uncertainty around safety estimates. Particularly valuable for regulatory submissions and evidence synthesis.',\n            'cumulative-incidence': 'Critical for time-to-event safety analysis, showing when adverse events typically occur during treatment. Use this to understand whether safety risks are early (treatment initiation) or late (long-term exposure) and to compare how quickly different treatments reach concerning safety thresholds.'\n        };\n        return useCases[vizId] || 'This visualization provides unique insights into treatment comparisons that can inform clinical decision-making and strategic planning.';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Comparator Tool | IBD CI Solution\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Compare and analyze competitive intelligence data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 border-r bg-white border-gray-200 flex flex-col`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/gd-logo.png\",\n                                                    alt: \"GlobalData\",\n                                                    className: \"h-8 w-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/gd-icon.png\",\n                                                    alt: \"GlobalData\",\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                                className: \"text-gray-600 hover:bg-gray-100\",\n                                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronLeft, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 34\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronRight, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 72\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-4 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-2\",\n                                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        !sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `flex items-center justify-center px-3 py-2 rounded-md cursor-pointer transition-colors ${item.label === 'Comparator Tool' ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100'}`,\n                                                                        onClick: ()=>item.children && sidebarOpen && toggleSection(index),\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipContent, {\n                                                                    side: \"right\",\n                                                                    className: \"ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: item.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${item.label === 'Clinical Trials Comparator' ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100'}`,\n                                                            onClick: ()=>{\n                                                                if (item.label === 'Home') {\n                                                                    window.location.href = '/';\n                                                                } else if ('href' in item && item.href && typeof item.href === 'string' && item.href !== '/comparator') {\n                                                                    window.location.href = item.href;\n                                                                } else if (item.children && sidebarOpen) {\n                                                                    toggleSection(index);\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        item.icon,\n                                                                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium\",\n                                                                            children: item.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 43\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                item.children && sidebarOpen && (expandedSections[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronUp, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronDown, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        item.children && sidebarOpen && expandedSections[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-6 mt-2 space-y-1\",\n                                                            children: item.children.map((child, childIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `flex items-center space-x-3 px-3 py-1 rounded-md cursor-pointer hover:bg-gray-50 ${child.label === 'Trial Results Comparator' ? 'text-blue-700 bg-blue-50 font-medium' : 'text-gray-600 hover:text-gray-900'}`,\n                                                                    onClick: ()=>{\n                                                                        if (child.href) {\n                                                                            window.location.href = child.href;\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        child.icon,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs\",\n                                                                            children: child.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, childIndex, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        sidebarOpen && _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.documentLibrary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 border border-blue-100 rounded-lg p-3 mx-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xs font-semibold text-blue-800 uppercase tracking-wide\",\n                                                                children: _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.documentLibrary.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-blue-700 hover:text-blue-900 hover:bg-blue-100 transition-colors\",\n                                                            children: [\n                                                                _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.documentLibrary.link?.icon && getIconComponent(_data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.documentLibrary.link.icon),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium\",\n                                                                    children: _data_navigation_config_json__WEBPACK_IMPORTED_MODULE_6__.documentLibrary.link?.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors mx-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.MessageSquare, {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"Help us improve this solution\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border-b border-gray-200 px-8 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: \"Trial Results Comparator\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: \"Solution prepared by Deallus for Roche\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full cursor-pointer hover:bg-blue-200 transition-colors\",\n                                                                onClick: ()=>setDiseaseDropdownOpen(!diseaseDropdownOpen),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-800\",\n                                                                        children: \"IBD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronDown, {\n                                                                        className: `w-3 h-3 text-blue-600 transition-transform ${diseaseDropdownOpen ? 'rotate-180' : ''}`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            diseaseDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-4 py-2 border-b border-gray-100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-semibold text-gray-900\",\n                                                                                children: \"Available Disease Areas\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-600\",\n                                                                                children: \"Switch between your accessible solutions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"py-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between bg-blue-50 border-l-2 border-blue-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-blue-700\",\n                                                                                            children: \"IBD\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 483,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm text-gray-600\",\n                                                                                            children: \"Inflammatory Bowel Disease\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 484,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-blue-600 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors border border-gray-200 bg-white shadow-sm\",\n                                                        onClick: ()=>{\n                                                            setConsultantDropdownOpen(!consultantDropdownOpen);\n                                                            setDiseaseDropdownOpen(false);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                                            children: \"Your Solution Consultant\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"James Davidson\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronDown, {\n                                                                    className: `w-4 h-4 text-gray-400 transition-transform ${consultantDropdownOpen ? 'rotate-180' : ''}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    consultantDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-3 border-b border-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 rounded-full overflow-hidden border-2 border-gray-100 shadow-sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 527,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 528,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/20 to-slate-200/30\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 529,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold text-gray-900\",\n                                                                                    children: \"James Davidson\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Senior Solution Consultant\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"py-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Mail, {\n                                                                                className: \"w-4 h-4 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Phone, {\n                                                                                className: \"w-4 h-4 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 544,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"+44 20 7936 6400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Video, {\n                                                                                className: \"w-4 h-4 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Schedule Teams Call\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-8 py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Compare Clinial Trial Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600\",\n                                                    children: \"Choose your starting point to begin your trial results comparison\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                                            children: primaryActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    className: `cursor-pointer transition-all duration-200 h-48 flex flex-col ${action.color} ${selectedAction === action.id ? 'ring-2 ring-blue-500' : ''}`,\n                                                    onClick: ()=>handleActionClick(action.id),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                            className: \"pb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    action.icon,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                        className: \"text-lg\",\n                                                                        children: action.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                            className: \"flex-1 flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                                    className: \"text-sm mb-3 flex-1\",\n                                                                    children: action.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                action.hasSavedOptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"w-full justify-between text-left\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                setSavedVisualizationsDropdownOpen(!savedVisualizationsDropdownOpen);\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: selectedSavedVisualization ? savedVisualizations.find((v)=>v.id === selectedSavedVisualization)?.name : \"Select Saved Comparators\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ChevronDown, {\n                                                                                    className: `w-3 h-3 transition-transform ${savedVisualizationsDropdownOpen ? 'rotate-180' : ''}`\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        savedVisualizationsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\",\n                                                                            children: savedVisualizations.map((savedViz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: `px-3 py-2 cursor-pointer transition-colors hover:bg-gray-50 ${selectedSavedVisualization === savedViz.id ? 'bg-blue-50 text-blue-700' : ''}`,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSavedVisualizationClick(savedViz);\n                                                                                        setSavedVisualizationsDropdownOpen(false);\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: savedViz.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                lineNumber: 624,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-gray-600\",\n                                                                                                children: [\n                                                                                                    savedViz.type,\n                                                                                                    \" • \",\n                                                                                                    savedViz.description\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                lineNumber: 625,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            savedViz.sharedBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-600\",\n                                                                                                children: [\n                                                                                                    \"Shared by \",\n                                                                                                    savedViz.sharedBy\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                lineNumber: 627,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, savedViz.id, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 33\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, action.id, true, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-lg font-semibold text-green-800 mb-2\",\n                                                                                        children: \"Latest Comparator Data\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 650,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-green-700\",\n                                                                                        children: \"Download the complete Excel dataset with all trial comparisons\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 651,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-green-600 mt-1\",\n                                                                                        children: \"Updated: December 2024 • Version 2.3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                size: \"lg\",\n                                                                                className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                                                onClick: ()=>{\n                                                                                    // Simulate download\n                                                                                    const link = document.createElement('a');\n                                                                                    link.href = '#';\n                                                                                    link.download = 'ibd-trial-comparator-v2.3.xlsx';\n                                                                                    link.click();\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                                        className: \"w-5 h-5 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                        lineNumber: 665,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    \"Download Excel\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                                            children: \"Version History\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: versionHistory.map((version, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-start space-x-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-shrink-0 relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-3 h-3 bg-blue-600 rounded-full border-2 border-white shadow\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 678,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                index < versionHistory.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"absolute left-1.5 top-3 w-0.5 h-12 bg-gray-200\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 680,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 677,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 min-w-0\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center justify-between\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                                                            children: version.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 685,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: version.date\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 686,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 684,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"text-xs text-gray-600 mt-1\",\n                                                                                                    children: version.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 688,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 683,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, version.id, true, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 27\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                            className: \"p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                                                    children: \"Pre-configured Comparators\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-lg font-semibold text-blue-700 mb-4 flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Target, {\n                                                                                            className: \"w-5 h-5 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 707,\n                                                                                            columnNumber: 27\n                                                                                        }, undefined),\n                                                                                        \"Efficacy\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 706,\n                                                                                    columnNumber: 25\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-3\",\n                                                                                    children: preConfiguredAnalyses.slice(0, 4).map((analysis)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                                            className: \"cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50\",\n                                                                                            onClick: ()=>handleVisualizationPreview(analysis),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                                                className: \"px-3 py-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-start space-x-3\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"flex-shrink-0 mt-1\",\n                                                                                                            children: analysis.icon\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 719,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"flex-1 min-w-0\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                                    className: \"font-medium text-sm text-gray-900 leading-tight mb-1\",\n                                                                                                                    children: analysis.label\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                                    lineNumber: 723,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                    className: \"text-xs text-gray-600 leading-tight\",\n                                                                                                                    children: analysis.description\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                                    lineNumber: 724,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 722,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 718,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                lineNumber: 717,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        }, analysis.id, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 25\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-lg font-semibold text-red-700 mb-4 flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Shield, {\n                                                                                            className: \"w-5 h-5 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 736,\n                                                                                            columnNumber: 27\n                                                                                        }, undefined),\n                                                                                        \"Safety\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 25\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-3\",\n                                                                                    children: preConfiguredAnalyses.slice(4, 8).map((analysis)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                                            className: \"cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50\",\n                                                                                            onClick: ()=>handleVisualizationPreview(analysis),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                                                className: \"px-3 py-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-start space-x-3\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"flex-shrink-0 mt-1\",\n                                                                                                            children: analysis.icon\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 748,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"flex-1 min-w-0\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                                    className: \"font-medium text-sm text-gray-900 leading-tight mb-1\",\n                                                                                                                    children: analysis.label\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                                    lineNumber: 752,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                    className: \"text-xs text-gray-600 leading-tight\",\n                                                                                                                    children: analysis.description\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                                    lineNumber: 753,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                            lineNumber: 751,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                    lineNumber: 747,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                                lineNumber: 746,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        }, analysis.id, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 741,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 739,\n                                                                                    columnNumber: 25\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedAction && selectedVisualization && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Selected:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \" \",\n                                                            primaryActions.find((a)=>a.id === selectedAction)?.title,\n                                                            \" → \",\n                                                            preConfiguredAnalyses.find((v)=>v.id === selectedVisualization)?.label\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedSavedVisualization && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-8 p-4 bg-indigo-50 border border-indigo-200 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-indigo-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Loading saved visualization:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \" \",\n                                                            savedVisualizations.find((v)=>v.id === selectedSavedVisualization)?.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        selectedVisualizationPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold text-gray-900\",\n                                                                            children: selectedVisualizationPreview.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                                            children: selectedVisualizationPreview.chartType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>setSelectedVisualizationPreview(null),\n                                                                    className: \"text-gray-500 hover:text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sr-only\",\n                                                                            children: \"Close\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"✕\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-full h-80 bg-gray-200 rounded-lg mb-6 overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-full bg-gradient-to-br from-gray-100 to-gray-300 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-24 h-24 text-gray-400 opacity-50\",\n                                                                        children: selectedVisualizationPreview.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-6 max-w-lg text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                                children: \"About this analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-700 leading-relaxed\",\n                                                                                children: selectedVisualizationPreview.detailedDescription\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 818,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>setSelectedVisualizationPreview(null),\n                                                                    children: \"Close\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: ()=>{\n                                                                        window.location.href = selectedVisualizationPreview.path;\n                                                                    },\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                                                    children: \"Try this visualization\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-8 mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-6 text-center\",\n                                                    children: \"About indirect comparisons\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-4xl mx-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-amber-50 border border-amber-200 rounded-lg p-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bookmark_Bot_Building2_Calendar_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Database_Download_FileText_FlaskConical_Globe_Grid3X3_Home_Lightbulb_LineChart_Mail_Map_MessageSquare_Phone_Scale_Search_Settings_Shield_Target_TreePine_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Lightbulb, {\n                                                                            className: \"w-5 h-5 text-amber-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-medium text-amber-900 mb-3\",\n                                                                            children: \"Important Limitations\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 864,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-amber-800 space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Indirect comparisons allow for evaluating treatments that have not been directly compared in head-to-head trials. However, these analyses have inherent limitations that should be carefully considered when interpreting results.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Key considerations include:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                            lineNumber: 871,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Population differences across trials, varying study designs and methodologies, differences in outcome definitions and measurement timepoints, and potential confounding factors that may affect comparability. Results should be interpreted with caution and clinical judgment is essential.\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 870,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"The strength of indirect comparison evidence is generally considered lower than direct head-to-head trial evidence. These analyses are most reliable when comparing treatments evaluated in similar patient populations using comparable methodologies.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Avinash\\\\Avinash_CISolution\\\\CISolution\\\\src\\\\GDWeb\\\\pages\\\\comparator.tsx\",\n        lineNumber: 311,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Comparator);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/comparator.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Activity,BarChart3,Bookmark,Bot,Building2,Calendar,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Database,Download,FileText,FlaskConical,Globe,Grid3X3,Home,Lightbulb,LineChart,Mail,Map,MessageSquare,Phone,Scale,Search,Settings,Shield,Target,TreePine,TrendingUp,Users,Video,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Activity,BarChart3,Bookmark,Bot,Building2,Calendar,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Database,Download,FileText,FlaskConical,Globe,Grid3X3,Home,Lightbulb,LineChart,Mail,Map,MessageSquare,Phone,Scale,Search,Settings,Shield,Target,TreePine,TrendingUp,Users,Video,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Activity: () => (/* reexport safe */ _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_chart_column_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Bookmark: () => (/* reexport safe */ _icons_bookmark_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Building2: () => (/* reexport safe */ _icons_building_2_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   ChevronDown: () => (/* reexport safe */ _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   ChevronUp: () => (/* reexport safe */ _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   Database: () => (/* reexport safe */ _icons_database_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   FlaskConical: () => (/* reexport safe */ _icons_flask_conical_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   Globe: () => (/* reexport safe */ _icons_globe_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   Grid3X3: () => (/* reexport safe */ _icons_grid_3x3_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   Lightbulb: () => (/* reexport safe */ _icons_lightbulb_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   LineChart: () => (/* reexport safe */ _icons_chart_line_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   Map: () => (/* reexport safe */ _icons_map_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   Phone: () => (/* reexport safe */ _icons_phone_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   Scale: () => (/* reexport safe */ _icons_scale_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   Target: () => (/* reexport safe */ _icons_target_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   TreePine: () => (/* reexport safe */ _icons_tree_pine_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/activity.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _icons_chart_column_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chart-column.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _icons_bookmark_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/bookmark.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/bot.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_building_2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/building-2.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/calendar.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/chevron-down.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/chevron-up.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _icons_database_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/database.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/file-text.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_flask_conical_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./icons/flask-conical.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _icons_globe_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./icons/globe.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _icons_grid_3x3_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./icons/grid-3x3.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./icons/house.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_lightbulb_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./icons/lightbulb.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _icons_chart_line_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./icons/chart-line.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./icons/mail.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_map_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./icons/map.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./icons/message-square.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _icons_phone_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./icons/phone.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _icons_scale_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./icons/scale.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./icons/search.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./icons/settings.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _icons_target_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./icons/target.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _icons_tree_pine_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./icons/tree-pine.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/tree-pine.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./icons/trending-up.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./icons/users.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./icons/video.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Activity,BarChart3,Bookmark,Bot,Building2,Calendar,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Database,Download,FileText,FlaskConical,Globe,Grid3X3,Home,Lightbulb,LineChart,Mail,Map,MessageSquare,Phone,Scale,Search,Settings,Shield,Target,TreePine,TrendingUp,Users,Video,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "@radix-ui/react-tooltip":
/*!******************************************!*\
  !*** external "@radix-ui/react-tooltip" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-tooltip");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcomparator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccomparator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();