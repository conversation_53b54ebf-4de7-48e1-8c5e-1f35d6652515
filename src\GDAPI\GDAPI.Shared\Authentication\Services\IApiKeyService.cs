using GDAPI.Shared.Authentication.Models;

namespace GDAPI.Shared.Authentication.Services;

/// <summary>
/// Service for API key validation and management
/// </summary>
public interface IApiKeyService
{
    /// <summary>
    /// Validate an API key
    /// </summary>
    /// <param name="apiKey">The API key to validate</param>
    /// <param name="clientIpAddress">Client IP address for rate limiting</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey, string? clientIpAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate a new API key
    /// </summary>
    /// <param name="name">Name for the API key</param>
    /// <param name="clientName">Client or application name</param>
    /// <param name="rateLimit">Rate limit for this key (optional)</param>
    /// <param name="expiresAt">Expiration date (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated API key</returns>
    Task<string> GenerateApiKeyAsync(string name, string clientName, int? rateLimit = null, DateTime? expiresAt = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke an API key
    /// </summary>
    /// <param name="apiKey">The API key to revoke</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if revoked successfully</returns>
    Task<bool> RevokeApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get API key information
    /// </summary>
    /// <param name="apiKey">The API key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API key information or null if not found</returns>
    Task<ApiKeyInfo?> GetApiKeyInfoAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update last used timestamp for an API key
    /// </summary>
    /// <param name="apiKey">The API key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateLastUsedAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all API keys (for management purposes)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of API key information</returns>
    Task<IEnumerable<ApiKeyInfo>> GetAllApiKeysAsync(CancellationToken cancellationToken = default);
}
