using Microsoft.AspNetCore.Mvc;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Modules.ClinicalTrialsComparator.Application.DTOs;
using GDAPI.Modules.ClinicalTrialsComparator.Application.Services;

namespace GDAPI.Web.Controllers;

/// <summary>
/// Clinical Trials Comparator controller for ClinicalTrialsComparator module
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ClinicalTrialsComparatorController : ControllerBase
{
    private readonly IClinicalTrialComparatorService _clinicalTrialComparatorService;
    private readonly ILogger<ClinicalTrialsComparatorController> _logger;

    public ClinicalTrialsComparatorController(
        IClinicalTrialComparatorService clinicalTrialComparatorService,
        ILogger<ClinicalTrialsComparatorController> logger)
    {
        _clinicalTrialComparatorService = clinicalTrialComparatorService ?? throw new ArgumentNullException(nameof(clinicalTrialComparatorService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get paginated clinical trial comparator data using stored procedure GetCITrialResultsComparator_Grid
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="moleculeType">Molecule type filter</param>
    /// <param name="mechanismOfAction">Mechanism of action filter</param>
    /// <param name="assetName">Asset name filter</param>
    /// <param name="indication">Indication filter</param>
    /// <param name="phase">Phase filter</param>
    /// <param name="trialName">Trial name filter</param>
    /// <param name="umbrellaEndPoints">Umbrella endpoints filter</param>
    /// <param name="endPoints">Endpoints filter</param>
    /// <param name="eTimePoint">E timepoint filter</param>
    /// <param name="dataHandling">Data handling filter</param>
    /// <param name="eventType">Event type filter</param>
    /// <param name="sTimepoint">S timepoint filter</param>
    /// <param name="dataType">Data type filter (default: 'Efficacy')</param>
    /// <returns>Paginated list of clinical trial comparator data</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<ClinicalTrialComparatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<PagedResult<ClinicalTrialComparatorDto>>>> GetClinicalTrialsComparator(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? moleculeType = null,
        [FromQuery] string? mechanismOfAction = null,
        [FromQuery] string? assetName = null,
        [FromQuery] string? indication = null,
        [FromQuery] string? phase = null,
        [FromQuery] string? trialName = null,
        [FromQuery] string? umbrellaEndPoints = null,
        [FromQuery] string? endPoints = null,
        [FromQuery] string? eTimePoint = null,
        [FromQuery] string? dataHandling = null,
        [FromQuery] string? eventType = null,
        [FromQuery] string? sTimepoint = null,
        [FromQuery] string dataType = "Efficacy")
    {
        _logger.LogInformation("Getting clinical trial comparator data with filters. MoleculeType: {MoleculeType}, AssetName: {AssetName}, Indication: {Indication}",
            moleculeType, assetName, indication);

        var request = new ClinicalTrialComparatorSearchRequest
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            MoleculeType = moleculeType,
            MechanismOfAction = mechanismOfAction,
            AssetName = assetName,
            Indication = indication,
            Phase = phase,
            TrialName = trialName,
            UmbrellaEndPoints = umbrellaEndPoints,
            EndPoints = endPoints,
            ETimePoint = eTimePoint,
            DataHandling = dataHandling,
            EventType = eventType,
            STimepoint = sTimepoint,
            DataType = dataType
        };

        var response = await _clinicalTrialComparatorService.GetClinicalTrialComparatorPagedAsync(request);
        return Ok(response);
    }

    /// <summary>
    /// Get available filter options for clinical trial comparator data
    /// </summary>
    /// <returns>Available filter options</returns>
    [HttpGet("filters")]
    [ProducesResponseType(typeof(ApiResponse<ClinicalTrialComparatorFiltersDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ClinicalTrialComparatorFiltersDto>>> GetFilters()
    {
        _logger.LogInformation("Getting clinical trial comparator filter options");

        var response = await _clinicalTrialComparatorService.GetFiltersAsync();
        return Ok(response);
    }

    /// <summary>
    /// Get clinical trial comparator statistics
    /// </summary>
    /// <returns>Clinical trial comparator statistics</returns>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(ApiResponse<ClinicalTrialComparatorStatsDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ClinicalTrialComparatorStatsDto>>> GetStats()
    {
        _logger.LogInformation("Getting clinical trial comparator statistics");

        var response = await _clinicalTrialComparatorService.GetStatsAsync();
        return Ok(response);
    }

    /// <summary>
    /// Health check endpoint for the clinical trials comparator module
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new { Status = "Healthy", Module = "ClinicalTrialsComparator", Timestamp = DateTime.UtcNow });
    }
}
