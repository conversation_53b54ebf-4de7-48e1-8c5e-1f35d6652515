using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using GDAPI.Shared.Middleware;
using GDAPI.Shared.Exceptions;
using GDAPI.Shared.DTOs.Common;
using Microsoft.AspNetCore.Http.Features;

namespace GDAPI.UnitTests.Shared.Tests.Middleware;

public class GlobalExceptionMiddlewareTests
{
    private readonly Mock<ILogger<GlobalExceptionMiddleware>> _loggerMock;
    private readonly RequestDelegate _nextMock;
    private readonly GlobalExceptionMiddleware _middleware;

    public GlobalExceptionMiddlewareTests()
    {
        _loggerMock = new Mock<ILogger<GlobalExceptionMiddleware>>();
        _nextMock = (HttpContext context) => 
        {
            return context.Items["ThrowException"] is Exception ex
                ? Task.FromException(ex)
                : Task.CompletedTask;
        };
        _middleware = new GlobalExceptionMiddleware(_nextMock, _loggerMock.Object);
    }

    [Fact]
    public async Task InvokeAsync_NoException_CallsNextDelegate()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.OK);
        responseBody.Length.Should().Be(0);
    }

    [Fact]
    public async Task InvokeAsync_WithBusinessException_ReturnsCorrectStatusAndResponse()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var exception = new BusinessException("Business rule violated");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Business rule violated");
        response.Error.Code.Should().Be("BUSINESS_RULE_VIOLATION");
        response.Error.CorrelationId.Should().Be(exception.CorrelationId);
    }

    [Fact]
    public async Task InvokeAsync_WithValidationException_ReturnsCorrectStatusAndResponse()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var validationErrors = new Dictionary<string, string[]>
        {
            { "Name", new[] { "Name is required" } },
            { "Email", new[] { "Email is invalid" } }
        };
        var exception = new ValidationException("Validation failed", validationErrors);
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Validation failed");
        response.Error.Code.Should().Be("VALIDATION_ERROR");
        response.Error.Details.Should().NotBeNull();
        response.Error.Details.Should().BeEquivalentTo(validationErrors);
    }

    [Fact]
    public async Task InvokeAsync_WithNotFoundException_ReturnsCorrectStatusAndResponse()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var exception = new NotFoundException("User", "123");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.NotFound);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("User with ID '123' was not found");
        response.Error.Code.Should().Be("RESOURCE_NOT_FOUND");
    }

    [Fact]
    public async Task InvokeAsync_WithUnauthorizedException_ReturnsCorrectStatusAndResponse()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var exception = new UnauthorizedException("Invalid credentials");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.Unauthorized);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("Invalid credentials");
        response.Error.Code.Should().Be("UNAUTHORIZED_ACCESS");
    }

    [Fact]
    public async Task InvokeAsync_WithGenericException_ReturnsInternalServerError()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var exception = new InvalidOperationException("Something went wrong");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.StatusCode.Should().Be((int)HttpStatusCode.InternalServerError);
        context.Response.ContentType.Should().Be("application/json");
        
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Success.Should().BeFalse();
        response.Error.Should().NotBeNull();
        response.Error!.Message.Should().Be("An unexpected error occurred");
        response.Error.Code.Should().Be("INTERNAL_SERVER_ERROR");
    }

    [Fact]
    public async Task InvokeAsync_WithCorrelationId_PreservesExistingCorrelationId()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var correlationId = "existing-correlation-id";
        context.Request.Headers["X-Correlation-ID"] = correlationId;
        
        var exception = new BusinessException("Business rule violated");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        response.Should().NotBeNull();
        response!.Error.Should().NotBeNull();
        response.Error!.CorrelationId.Should().Be(correlationId);
    }

    [Fact]
    public async Task InvokeAsync_WithException_LogsError()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var exception = new InvalidOperationException("Something went wrong");
        context.Items["ThrowException"] = exception;

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Something went wrong")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
